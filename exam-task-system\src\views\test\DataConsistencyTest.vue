<!-- {{ AURA-X: Add - 创建数据一致性测试页面. Approval: 寸止(ID:1734684000). }} -->
<template>
  <div class="data-consistency-test">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>数据一致性测试</span>
          <el-tag :type="consistencyStatus.type">{{ consistencyStatus.text }}</el-tag>
        </div>
      </template>

      <!-- 用户信息 -->
      <div class="user-section">
        <h3>用户信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ authStore.user?.id || '未登录' }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ authStore.user?.username || '未登录' }}</el-descriptions-item>
          <el-descriptions-item label="角色">{{ authStore.user?.role || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="认证状态">
            <el-tag :type="authStore.isAuthenticated ? 'success' : 'danger'">
              {{ authStore.isAuthenticated ? '已认证' : '未认证' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 学生端数据对比 -->
      <div v-if="authStore.isStudent" class="student-data-section">
        <h3>学生端数据对比</h3>
        
        <!-- 数据源状态 -->
        <div class="data-source-status">
          <el-alert
            title="数据源状态"
            :type="studentStore.isLoading ? 'warning' : 'success'"
            :description="`最后更新时间: ${formatTime(studentStore.lastUpdateTime)}`"
            show-icon
          />
        </div>

        <!-- 绑定信息对比 -->
        <el-card class="comparison-card">
          <template #header>绑定信息</template>
          <el-row :gutter="20">
            <el-col :span="12">
              <h4>统一数据源 (studentStore)</h4>
              <el-descriptions :column="1" size="small" border>
                <el-descriptions-item label="绑定状态">
                  <el-tag :type="studentStore.hasBinding ? 'success' : 'warning'">
                    {{ studentStore.hasBinding ? '已绑定' : '未绑定' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="监督者">
                  {{ studentStore.supervisorInfo?.username || '无' }}
                </el-descriptions-item>
                <el-descriptions-item label="绑定ID">
                  {{ studentStore.bindingInfo?.id || '无' }}
                </el-descriptions-item>
              </el-descriptions>
            </el-col>
            <el-col :span="12">
              <h4>直接API调用</h4>
              <el-descriptions :column="1" size="small" border>
                <el-descriptions-item label="绑定状态">
                  <el-tag :type="directApiData.binding?.status === 'active' ? 'success' : 'warning'">
                    {{ directApiData.binding?.status || '未知' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="监督者">
                  {{ directApiData.binding?.supervisor?.username || '无' }}
                </el-descriptions-item>
                <el-descriptions-item label="绑定ID">
                  {{ directApiData.binding?.id || '无' }}
                </el-descriptions-item>
              </el-descriptions>
            </el-col>
          </el-row>
        </el-card>

        <!-- 任务统计对比 -->
        <el-card class="comparison-card">
          <template #header>任务统计</template>
          <el-row :gutter="20">
            <el-col :span="12">
              <h4>统一数据源 (studentStore)</h4>
              <el-descriptions :column="1" size="small" border>
                <el-descriptions-item label="已接受任务">{{ studentStore.taskStats.accepted_tasks }}</el-descriptions-item>
                <el-descriptions-item label="已完成任务">{{ studentStore.taskStats.completed_tasks }}</el-descriptions-item>
                <el-descriptions-item label="总积分">{{ studentStore.taskStats.total_points }}</el-descriptions-item>
                <el-descriptions-item label="完成率">{{ studentStore.taskStats.completion_rate }}%</el-descriptions-item>
                <el-descriptions-item label="学习时长">{{ Math.round(studentStore.taskStats.total_study_time / 60 * 10) / 10 }}小时</el-descriptions-item>
              </el-descriptions>
            </el-col>
            <el-col :span="12">
              <h4>直接API调用</h4>
              <el-descriptions :column="1" size="small" border>
                <el-descriptions-item label="已接受任务">{{ directApiData.taskStats?.accepted_tasks || 0 }}</el-descriptions-item>
                <el-descriptions-item label="已完成任务">{{ directApiData.taskStats?.completed_tasks || 0 }}</el-descriptions-item>
                <el-descriptions-item label="总积分">{{ directApiData.taskStats?.total_points || 0 }}</el-descriptions-item>
                <el-descriptions-item label="完成率">{{ directApiData.taskStats?.completion_rate || 0 }}%</el-descriptions-item>
                <el-descriptions-item label="学习时长">{{ Math.round((directApiData.taskStats?.total_study_time || 0) / 60 * 10) / 10 }}小时</el-descriptions-item>
              </el-descriptions>
            </el-col>
          </el-row>
        </el-card>

        <!-- 计算属性对比 -->
        <el-card class="comparison-card">
          <template #header>计算属性</template>
          <el-row :gutter="20">
            <el-col :span="12">
              <h4>统一数据源计算</h4>
              <el-descriptions :column="1" size="small" border>
                <el-descriptions-item label="今日积分">{{ studentStore.todayPoints }}</el-descriptions-item>
                <el-descriptions-item label="本周完成任务">{{ studentStore.weeklyCompletedTasks }}</el-descriptions-item>
                <el-descriptions-item label="今日学习时长">{{ studentStore.studyHoursToday }}小时</el-descriptions-item>
                <el-descriptions-item label="我的任务数">{{ studentStore.myTasks.length }}</el-descriptions-item>
                <el-descriptions-item label="可用任务数">{{ studentStore.availableTasks.length }}</el-descriptions-item>
              </el-descriptions>
            </el-col>
            <el-col :span="12">
              <h4>localStorage数据</h4>
              <el-descriptions :column="1" size="small" border>
                <el-descriptions-item label="localStorage任务">{{ localStorageData.myTasks.length }}</el-descriptions-item>
                <el-descriptions-item label="localStorage积分">{{ localStorageData.points }}</el-descriptions-item>
                <el-descriptions-item label="localStorage连续天数">{{ localStorageData.streak }}</el-descriptions-item>
                <el-descriptions-item label="localStorage修复石">{{ localStorageData.stones }}</el-descriptions-item>
                <el-descriptions-item label="全局任务">{{ localStorageData.globalTasks.length }}</el-descriptions-item>
              </el-descriptions>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <!-- 操作按钮 -->
      <div class="actions-section">
        <h3>测试操作</h3>
        <el-space wrap>
          <el-button type="primary" @click="refreshAllData" :loading="loading">
            刷新所有数据
          </el-button>
          <el-button type="success" @click="compareData">
            对比数据一致性
          </el-button>
          <el-button type="warning" @click="clearLocalStorage">
            清空localStorage
          </el-button>
          <el-button type="info" @click="exportData">
            导出数据
          </el-button>
        </el-space>
      </div>

      <!-- 一致性检查结果 -->
      <div class="consistency-results">
        <h3>一致性检查结果</h3>
        <div v-for="(result, index) in consistencyResults" :key="index" class="result-item">
          <el-alert
            :title="result.title"
            :type="result.type"
            :description="result.description"
            show-icon
            :closable="false"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { useStudentStore } from '@/stores/student'
import { bindingAPI } from '@/api/binding'
import { taskAPI } from '@/api/task'

// 状态管理
const authStore = useAuthStore()
const studentStore = useStudentStore()

// 响应式数据
const loading = ref(false)
const directApiData = ref({
  binding: null,
  taskStats: null,
  myTasks: [],
  availableTasks: []
})

const localStorageData = ref({
  myTasks: [],
  points: 0,
  streak: 0,
  stones: 0,
  globalTasks: []
})

const consistencyResults = ref([])

// 计算属性
const consistencyStatus = computed(() => {
  const hasErrors = consistencyResults.value.some(r => r.type === 'error')
  const hasWarnings = consistencyResults.value.some(r => r.type === 'warning')
  
  if (hasErrors) {
    return { type: 'danger', text: '数据不一致' }
  } else if (hasWarnings) {
    return { type: 'warning', text: '部分不一致' }
  } else if (consistencyResults.value.length > 0) {
    return { type: 'success', text: '数据一致' }
  } else {
    return { type: 'info', text: '未检查' }
  }
})

// 方法
const formatTime = (date) => {
  if (!date) return '从未更新'
  return new Date(date).toLocaleString()
}

const refreshAllData = async () => {
  loading.value = true
  try {
    // 刷新统一数据源
    await studentStore.refreshAllData()
    
    // 刷新直接API数据
    await loadDirectApiData()
    
    // 刷新localStorage数据
    loadLocalStorageData()
    
    ElMessage.success('数据刷新完成')
  } catch (error) {
    console.error('刷新数据失败:', error)
    ElMessage.error('刷新数据失败')
  } finally {
    loading.value = false
  }
}

const loadDirectApiData = async () => {
  try {
    // 直接调用API获取绑定信息
    const bindingResponse = await bindingAPI.getMyBinding()
    directApiData.value.binding = bindingResponse?.data || null
    directApiData.value.taskStats = bindingResponse?.data?.task_stats || null
    
    // 直接调用API获取任务
    const myTasksResponse = await taskAPI.getMyTasks()
    directApiData.value.myTasks = myTasksResponse?.data || []
    
    const availableTasksResponse = await taskAPI.getFixedTasks()
    directApiData.value.availableTasks = availableTasksResponse?.data || []
    
  } catch (error) {
    console.error('加载直接API数据失败:', error)
  }
}

const loadLocalStorageData = () => {
  try {
    localStorageData.value = {
      myTasks: JSON.parse(localStorage.getItem('myTasks') || '[]'),
      points: parseInt(localStorage.getItem('userPoints') || '0'),
      streak: parseInt(localStorage.getItem('consecutiveDays') || '0'),
      stones: parseInt(localStorage.getItem('repairStones') || '0'),
      globalTasks: JSON.parse(localStorage.getItem('globalTasks') || '[]')
    }
  } catch (error) {
    console.error('加载localStorage数据失败:', error)
  }
}

const compareData = () => {
  const results = []
  
  // 检查绑定信息一致性
  const storeBinding = studentStore.bindingInfo
  const apiBinding = directApiData.value.binding
  
  if (JSON.stringify(storeBinding) === JSON.stringify(apiBinding)) {
    results.push({
      title: '绑定信息一致性',
      type: 'success',
      description: '统一数据源与直接API调用的绑定信息完全一致'
    })
  } else {
    results.push({
      title: '绑定信息不一致',
      type: 'error',
      description: '统一数据源与直接API调用的绑定信息存在差异'
    })
  }
  
  // 检查任务统计一致性
  const storeStats = studentStore.taskStats
  const apiStats = directApiData.value.taskStats
  
  if (JSON.stringify(storeStats) === JSON.stringify(apiStats)) {
    results.push({
      title: '任务统计一致性',
      type: 'success',
      description: '统一数据源与直接API调用的任务统计完全一致'
    })
  } else {
    results.push({
      title: '任务统计不一致',
      type: 'error',
      description: '统一数据源与直接API调用的任务统计存在差异'
    })
  }
  
  // 检查localStorage依赖
  if (localStorageData.value.myTasks.length > 0) {
    results.push({
      title: 'localStorage残留数据',
      type: 'warning',
      description: `发现${localStorageData.value.myTasks.length}个localStorage任务记录，建议清理`
    })
  } else {
    results.push({
      title: 'localStorage清理状态',
      type: 'success',
      description: 'localStorage中无残留任务数据'
    })
  }
  
  consistencyResults.value = results
}

const clearLocalStorage = () => {
  const keys = ['myTasks', 'userPoints', 'consecutiveDays', 'repairStones', 'globalTasks', 'taskStats']
  keys.forEach(key => localStorage.removeItem(key))
  loadLocalStorageData()
  ElMessage.success('localStorage已清空')
}

const exportData = () => {
  const data = {
    timestamp: new Date().toISOString(),
    studentStore: {
      bindingInfo: studentStore.bindingInfo,
      taskStats: studentStore.taskStats,
      myTasks: studentStore.myTasks,
      availableTasks: studentStore.availableTasks
    },
    directApiData: directApiData.value,
    localStorageData: localStorageData.value,
    consistencyResults: consistencyResults.value
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `data-consistency-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('数据已导出')
}

// 生命周期
onMounted(async () => {
  if (authStore.isStudent) {
    await refreshAllData()
    compareData()
  }
})
</script>

<style scoped>
.data-consistency-test {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-section,
.student-data-section,
.actions-section,
.consistency-results {
  margin-bottom: 30px;
}

.data-source-status {
  margin-bottom: 20px;
}

.comparison-card {
  margin-bottom: 20px;
}

.comparison-card h4 {
  margin-bottom: 10px;
  color: #409eff;
}

.result-item {
  margin-bottom: 10px;
}
</style>
