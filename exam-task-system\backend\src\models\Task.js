const db = require('../config/database');
const { createDocument, getDocumentById, updateDocument, deleteDocument, queryDocuments } = require('../utils/modelHelper');

// 集合名称
const COLLECTION = 'tasks';

/**
 * 创建新任务
 * @param {Object} taskData - 任务数据
 * @returns {Promise} - 返回创建的任务
 */
const createTask = async (taskData) => {
  try {
    return await createDocument(db, COLLECTION, {
      title: taskData.title,
      description: taskData.description || '',
      dueDate: taskData.dueDate,
      deadline: taskData.deadline || taskData.dueDate, // 添加deadline字段
      status: taskData.status || 'pending',
      points: taskData.points || 10,
      creatorId: taskData.creatorId,
      studentId: taskData.studentId || null,
      // 添加任务广场的完整字段
      subject: taskData.subject,
      difficulty: taskData.difficulty,
      type: taskData.type,
      target: taskData.target,
      requirements: taskData.requirements || [],
      completionCriteria: taskData.completionCriteria,
      isFixed: taskData.isFixed || false
    });
  } catch (error) {
    console.error('创建任务失败:', error);
    throw error;
  }
};

/**
 * 根据ID获取任务
 * @param {String} id - 任务ID
 * @returns {Promise} - 返回任务数据
 */
const getTaskById = async (id) => {
  try {
    return await getDocumentById(db, COLLECTION, id);
  } catch (error) {
    console.error('获取任务失败:', error);
    throw error;
  }
};

/**
 * 更新任务
 * @param {String} id - 任务ID
 * @param {Object} taskData - 要更新的任务数据
 * @returns {Promise} - 返回更新后的任务
 */
const updateTask = async (id, taskData) => {
  try {
    return await updateDocument(db, COLLECTION, id, taskData);
  } catch (error) {
    console.error('更新任务失败:', error);
    throw error;
  }
};

/**
 * 删除任务
 * @param {String} id - 任务ID
 * @returns {Promise} - 返回删除结果
 */
const deleteTask = async (id) => {
  try {
    return await deleteDocument(db, COLLECTION, id);
  } catch (error) {
    console.error('删除任务失败:', error);
    throw error;
  }
};

/**
 * 获取任务列表
 * @param {Object} query - 查询条件
 * @param {Object} options - 查询选项
 * @returns {Promise} - 返回任务列表
 */
const getTasks = async (query = {}, options = {}) => {
  try {
    // 默认按截止日期升序排序
    const defaultOptions = {
      orderBy: { field: 'dueDate', direction: 'asc' },
      ...options
    };
    
    return await queryDocuments(db, COLLECTION, query, defaultOptions);
  } catch (error) {
    console.error('获取任务列表失败:', error);
    throw error;
  }
};

/**
 * 获取学生的任务
 * @param {String} studentId - 学生ID
 * @param {Object} options - 查询选项
 * @returns {Promise} - 返回任务列表
 */
const getStudentTasks = async (studentId, options = {}) => {
  try {
    return await getTasks({ studentId }, options);
  } catch (error) {
    console.error('获取学生任务失败:', error);
    throw error;
  }
};

/**
 * 获取监督者创建的任务
 * @param {String} creatorId - 创建者ID
 * @param {Object} options - 查询选项
 * @returns {Promise} - 返回任务列表
 */
const getCreatorTasks = async (creatorId, options = {}) => {
  try {
    return await getTasks({ creatorId }, options);
  } catch (error) {
    console.error('获取创建者任务失败:', error);
    throw error;
  }
};

/**
 * 更新任务状态
 * @param {String} id - 任务ID
 * @param {String} status - 新状态
 * @returns {Promise} - 返回更新后的任务
 */
const updateTaskStatus = async (id, status) => {
  try {
    return await updateTask(id, { status });
  } catch (error) {
    console.error('更新任务状态失败:', error);
    throw error;
  }
};

/**
 * 删除学生的所有任务
 * @param {String} studentId - 学生ID
 * @returns {Promise} - 返回删除的任务数量
 */
const deleteTasksByStudent = async (studentId) => {
  try {
    const tasks = await getTasks({ studentId });
    let deletedCount = 0;

    for (const task of tasks) {
      await deleteTask(task._id);
      deletedCount++;
    }

    console.log(`删除学生 ${studentId} 的 ${deletedCount} 个任务`);
    return deletedCount;
  } catch (error) {
    console.error('删除学生任务失败:', error);
    throw error;
  }
};

module.exports = {
  createTask,
  getTaskById,
  updateTask,
  deleteTask,
  getTasks,
  getStudentTasks,
  getCreatorTasks,
  updateTaskStatus,
  deleteTasksByStudent
};