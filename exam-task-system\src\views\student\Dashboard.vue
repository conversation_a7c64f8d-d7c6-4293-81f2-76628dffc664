<template>
  <div class="page-container">
    <el-container class="min-h-screen">
      <!-- 侧边栏 -->
      <el-aside width="220px" class="bg-white border-r border-gray-200">
        <div class="p-4 border-b border-gray-200 flex items-center">
          <el-avatar :size="40" class="mr-3">
            <el-icon><UserFilled /></el-icon>
          </el-avatar>
          <div class="overflow-hidden">
            <div class="font-medium truncate">{{ authStore.user?.nickname }}</div>
            <div class="text-xs text-gray-500">考研者</div>
          </div>
        </div>
        
        <el-menu
          :default-active="activeMenu"
          class="border-0"
          router
        >
          <el-menu-item index="/student">
            <el-icon><HomeFilled /></el-icon>
            <span>首页</span>
          </el-menu-item>
          
          <el-menu-item index="/student/binding">
            <el-icon><Connection /></el-icon>
            <span>绑定管理</span>
          </el-menu-item>
          
          <el-menu-item index="/student-tasks">
            <el-icon><List /></el-icon>
            <span>任务广场</span>
          </el-menu-item>
          
          <el-menu-item index="/student/my-tasks">
            <el-icon><Document /></el-icon>
            <span>我的任务</span>
          </el-menu-item>
          
          <el-menu-item index="/student/statistics">
            <el-icon><DataAnalysis /></el-icon>
            <span>学习统计</span>
          </el-menu-item>
          
          <el-menu-item index="/student/rewards">
            <el-icon><Trophy /></el-icon>
            <span>我的奖励</span>
          </el-menu-item>
        </el-menu>
        
        <div class="absolute bottom-0 left-0 w-full p-4">
          <el-button type="danger" plain @click="logout" class="w-full">
            <el-icon class="mr-1"><SwitchButton /></el-icon>
            退出登录
          </el-button>
        </div>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container>
        <el-header height="60px" class="bg-white border-b border-gray-200 flex items-center justify-between px-6">
          <h2 class="text-lg font-medium">{{ pageTitle }}</h2>
          <div>
            <el-button type="primary" @click="goToTasks" v-if="activeMenu === '/student'">
              <el-icon class="mr-1"><List /></el-icon>
              任务广场
            </el-button>
          </div>
        </el-header>
        
        <el-main class="p-6">
          <!-- 子路由内容 -->
          <router-view v-if="$route.path !== '/student'" />
          
          <!-- 默认首页内容 -->
          <div v-else>
            <!-- 每日进度 -->
            <div class="card-container">
              <h3 class="text-lg font-medium mb-4">今日任务进度</h3>
              
              <div class="flex items-center mb-4">
                <div class="text-3xl font-bold mr-4">{{ stats.completedToday }}/{{ stats.requiredDaily }}</div>
                <el-progress 
                  :percentage="stats.completionPercentage" 
                  :status="stats.completionPercentage >= 100 ? 'success' : ''"
                  :stroke-width="20"
                  class="flex-1"
                />
              </div>
              
              <div class="text-sm text-gray-500">
                <template v-if="stats.completionPercentage >= 100">
                  <el-icon class="text-green-500 mr-1"><Check /></el-icon>
                  今日任务已完成，太棒了！
                </template>
                <template v-else>
                  <el-icon class="text-orange-500 mr-1"><Timer /></el-icon>
                  还需完成 {{ stats.requiredDaily - stats.completedToday }} 个任务
                </template>
              </div>
            </div>
            
            <!-- 统计卡片 -->
            <el-row :gutter="20" class="mt-6">
              <el-col :span="8">
                <div class="card-container h-32 flex flex-col justify-between">
                  <div class="text-gray-500">待完成任务</div>
                  <div class="flex items-end justify-between">
                    <div class="text-3xl font-bold">{{ stats.pendingTasks }}</div>
                    <el-icon class="text-primary-500 text-2xl"><Calendar /></el-icon>
                  </div>
                </div>
              </el-col>
              
              <el-col :span="8">
                <div class="card-container h-32 flex flex-col justify-between">
                  <div class="text-gray-500">累计完成任务</div>
                  <div class="flex items-end justify-between">
                    <div class="text-3xl font-bold">{{ stats.totalCompletedTasks }}</div>
                    <el-icon class="text-green-500 text-2xl"><Check /></el-icon>
                  </div>
                </div>
              </el-col>
              
              <el-col :span="8">
                <div class="card-container h-32 flex flex-col justify-between">
                  <div class="text-gray-500">获得奖励</div>
                  <div class="flex items-end justify-between">
                    <div class="text-3xl font-bold">{{ stats.totalRewards }}</div>
                    <el-icon class="text-orange-500 text-2xl"><Trophy /></el-icon>
                  </div>
                </div>
              </el-col>
            </el-row>
            
            <!-- 进行中的任务 -->
            <div class="card-container mt-6">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium">进行中的任务</h3>
                <el-button type="primary" text @click="goToMyTasks">
                  查看全部
                  <el-icon class="ml-1"><ArrowRight /></el-icon>
                </el-button>
              </div>
              
              <el-table :data="activeTasks" stripe style="width: 100%">
                <el-table-column prop="title" label="任务标题" />
                <el-table-column prop="subject" label="科目" width="100" />
                <el-table-column prop="deadline" label="截止时间" width="180" />
                <el-table-column label="操作" width="150">
                  <template #default="scope">
                    <el-button type="primary" size="small" @click="submitTask(scope.row)">
                      提交
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              
              <div v-if="activeTasks.length === 0" class="text-center py-8 text-gray-500">
                暂无进行中的任务
              </div>
            </div>
            
            <!-- 绑定状态 -->
            <div class="card-container mt-6">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium">绑定状态</h3>
                <el-button type="primary" text @click="goToBinding">
                  管理绑定
                  <el-icon class="ml-1"><ArrowRight /></el-icon>
                </el-button>
              </div>
              
              <div v-if="binding" class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                  <el-avatar :size="50" class="mr-4">
                    <el-icon><UserFilled /></el-icon>
                  </el-avatar>
                  <div>
                    <div class="font-medium text-lg">{{ binding.supervisor.nickname }}</div>
                    <div class="text-gray-500">监督者</div>
                  </div>
                </div>
                
                <div class="flex items-center">
                  <el-tag type="success" v-if="binding.status === 'active'">已绑定</el-tag>
                  <el-tag type="danger" v-else>未绑定</el-tag>
                </div>
              </div>
              
              <div v-else class="text-center py-8">
                <el-empty description="暂无绑定关系">
                  <el-button type="primary" @click="goToBinding">绑定监督者</el-button>
                </el-empty>
              </div>
            </div>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useStudentStore } from '@/stores/student'
import { 
  UserFilled, HomeFilled, Connection, List, Document, 
  DataAnalysis, Trophy, SwitchButton, Calendar, 
  Check, Timer, ArrowRight 
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const studentStore = useStudentStore()

// 页面标题映射
const pageTitles = {
  '/student': '考研者控制台',
  '/student/binding': '绑定管理',
  '/student/tasks': '任务广场',
  '/student/my-tasks': '我的任务',
  '/student/statistics': '学习统计',
  '/student/rewards': '我的奖励'
}

// 当前活跃菜单
const activeMenu = computed(() => {
  return route.path === '/student' ? '/student' : route.path
})

// 页面标题
const pageTitle = computed(() => {
  return pageTitles[route.path] || '考研者控制台'
})

// {{ AURA-X: Modify - 使用统一的学生端数据源. Approval: 寸止(ID:1734684000). }}
// 计算属性 - 从统一数据源获取
const stats = computed(() => ({
  completedToday: studentStore.weeklyCompletedTasks, // 使用本周完成任务数
  requiredDaily: 6, // 固定目标
  completionPercentage: studentStore.taskStats.completion_rate || 0,
  pendingTasks: studentStore.myTasks.filter(task => task.status === 'pending').length,
  totalCompletedTasks: studentStore.taskStats.completed_tasks || 0,
  totalRewards: Math.floor((studentStore.taskStats.total_points || 0) / 100) // 每100积分算1个奖励
}))

const activeTasks = computed(() => {
  // 获取进行中的任务，最多显示2个
  return studentStore.myTasks
    .filter(task => task.status === 'in_progress' || task.status === 'pending')
    .slice(0, 2)
    .map(task => ({
      id: task.id,
      title: task.title,
      subject: task.subject,
      deadline: task.deadline
    }))
})

const binding = computed(() => studentStore.bindingInfo)

// 方法
const logout = () => {
  authStore.logout()
  router.push('/login')
}

const goToTasks = () => {
  router.push('/student-tasks')
}

const goToMyTasks = () => {
  router.push('/student/my-tasks')
}

const goToBinding = () => {
  router.push('/student/binding')
}

const submitTask = (task) => {
  // 跳转到任务提交页面
  router.push(`/student/task/${task.id}/submit`)
}

onMounted(async () => {
  // 检查用户权限
  if (!authStore.isAuthenticated || !authStore.isStudent) {
    router.push('/login')
    return
  }

  // 初始化学生数据
  await studentStore.initStudentData()
})
</script>