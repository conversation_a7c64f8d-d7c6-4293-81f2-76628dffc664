// {{ AURA-X: Add - 创建统一的学生端数据管理. Approval: 寸止(ID:1734684000). }}
// 学生端统一数据管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { bindingAPI } from '@/api/binding'
import { taskAPI } from '@/api/task'
import { useAuthStore } from './auth'

export const useStudentStore = defineStore('student', () => {
  // 状态
  const isLoading = ref(false)
  const lastUpdateTime = ref(null)
  
  // 绑定信息
  const bindingInfo = ref(null)
  
  // 任务统计数据
  const taskStats = ref({
    accepted_tasks: 0,
    completed_tasks: 0,
    total_points: 0,
    completion_rate: 0,
    total_study_time: 0
  })
  
  // 我的任务列表
  const myTasks = ref([])
  
  // 可用任务列表
  const availableTasks = ref([])

  // 计算属性
  const isAuthenticated = computed(() => {
    const authStore = useAuthStore()
    return authStore.isAuthenticated && authStore.isStudent
  })

  const hasBinding = computed(() => {
    return bindingInfo.value && bindingInfo.value.status === 'active'
  })

  const supervisorInfo = computed(() => {
    return bindingInfo.value?.supervisor || null
  })

  const todayPoints = computed(() => {
    // 计算今日获得的积分
    const today = new Date()
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate())

    console.log('计算今日积分，任务数量:', myTasks.value.length)
    console.log('所有任务详情:', myTasks.value)

    const todayCompletedTasks = myTasks.value
      .filter(task => {
        // 兼容多种日期字段名
        const completedDate = task.completedAt || task.completionDate || task.endTime
        const dateObj = completedDate ? new Date(completedDate) : null
        const isCompleted = task.status === 'completed'
        // 如果没有完成日期，但状态是completed，假设是今天完成的
        const isToday = dateObj ? dateObj >= todayStart : isCompleted

        console.log('任务检查:', {
          title: task.title,
          status: task.status,
          completedDate,
          isCompleted,
          isToday,
          points: task.points
        })

        return isCompleted && isToday
      })

    const points = todayCompletedTasks.reduce((total, task) => total + (task.points || 0), 0)
    console.log('今日完成任务:', todayCompletedTasks.length, '总积分:', points)

    return points
  })

  const weeklyCompletedTasks = computed(() => {
    // 计算本周完成的任务数
    const now = new Date()
    const weekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay())
    
    return myTasks.value.filter(task => {
      const completedDate = task.completedAt ? new Date(task.completedAt) : null
      return task.status === 'completed' && 
             completedDate && 
             completedDate >= weekStart
    }).length
  })

  const studyHoursToday = computed(() => {
    // 计算今日学习时长（小时）
    const today = new Date()
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    
    const todayTasks = myTasks.value.filter(task => {
      const completedDate = task.completedAt ? new Date(task.completedAt) : null
      return task.status === 'completed' && 
             completedDate && 
             completedDate >= todayStart
    })
    
    const totalMinutes = todayTasks.reduce((total, task) => total + (task.timeSpent || 0), 0)
    return Math.round(totalMinutes / 60 * 10) / 10
  })

  // 方法
  
  /**
   * 初始化学生数据
   */
  const initStudentData = async () => {
    if (!isAuthenticated.value) {
      console.warn('用户未认证，无法初始化学生数据')
      return
    }

    try {
      isLoading.value = true
      
      // 并行获取数据
      await Promise.all([
        loadBindingInfo(),
        loadMyTasks(),
        loadAvailableTasks()
      ])
      
      lastUpdateTime.value = new Date()

      // 启用实时同步
      startRealtimeSync()

      console.log('学生数据初始化完成')

    } catch (error) {
      console.error('初始化学生数据失败:', error)
      ElMessage.error('数据加载失败，请刷新页面重试')
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 启用实时同步
   */
  const startRealtimeSync = () => {
    // 监听任务数据变更
    window.addEventListener('tasks-updated', (event) => {
      console.log('接收到任务实时更新:', event.detail)
      if (event.detail) {
        myTasks.value = event.detail
      }
    })

    // 监听绑定信息和统计数据变更
    window.addEventListener('bindings-updated', (event) => {
      console.log('接收到绑定信息实时更新:', event.detail)
      if (event.detail) {
        // 更新绑定信息和统计数据
        bindingInfo.value = event.detail.binding || null
        taskStats.value = event.detail.stats || {}
      }
    })

    // 监听学生统计数据变更
    window.addEventListener('student-stats-updated', (event) => {
      console.log('接收到学生统计实时更新:', event.detail)
      // 重新加载绑定信息以获取最新统计
      loadBindingInfo()
    })

    console.log('实时同步已启用')
  }
  }

  /**
   * 加载绑定信息和任务统计
   */
  const loadBindingInfo = async () => {
    try {
      const response = await bindingAPI.getMyBinding()
      
      if (response && response.data) {
        bindingInfo.value = response.data
        
        // 更新任务统计
        if (response.data.task_stats) {
          taskStats.value = {
            accepted_tasks: response.data.task_stats.accepted_tasks || 0,
            completed_tasks: response.data.task_stats.completed_tasks || 0,
            total_points: response.data.task_stats.total_points || 0,
            completion_rate: response.data.task_stats.completion_rate || 0,
            total_study_time: response.data.task_stats.total_study_time || 0
          }
        }
        
        console.log('绑定信息已更新:', bindingInfo.value)
        console.log('任务统计已更新:', taskStats.value)
      } else {
        bindingInfo.value = null
        console.log('未找到绑定信息')
      }
    } catch (error) {
      console.error('加载绑定信息失败:', error)
      if (error.response?.status !== 404) {
        throw error
      }
    }
  }

  /**
   * 加载我的任务
   */
  const loadMyTasks = async () => {
    try {
      console.log('开始加载我的任务...')
      const response = await taskAPI.getMyTasks()

      console.log('getMyTasks API响应:', response)

      if (response && response.data) {
        myTasks.value = response.data
        console.log('我的任务已更新:', myTasks.value.length, '个任务')
        console.log('任务详情:', myTasks.value)
      } else {
        console.log('API响应无数据，设置为空数组')
        myTasks.value = []
      }
    } catch (error) {
      console.error('加载我的任务失败:', error)
      console.error('错误详情:', error.response?.data || error.message)
      myTasks.value = []
      throw error
    }
  }

  /**
   * 加载可用任务
   */
  const loadAvailableTasks = async () => {
    try {
      const response = await taskAPI.getFixedTasks()
      
      if (response && response.data) {
        availableTasks.value = response.data
        console.log('可用任务已更新:', availableTasks.value.length, '个任务')
      } else {
        availableTasks.value = []
      }
    } catch (error) {
      console.error('加载可用任务失败:', error)
      availableTasks.value = []
      throw error
    }
  }

  /**
   * 接受任务
   */
  const acceptTask = async (task) => {
    try {
      // 修复API调用格式 - 后端期望 { taskData: task }
      const response = await taskAPI.acceptFixedTask(task)

      if (response && response.data) {
        // 重新加载我的任务和可用任务
        await Promise.all([
          loadMyTasks(),
          loadAvailableTasks()
        ])

        ElMessage.success('任务接受成功')
        return true
      } else {
        ElMessage.error('任务接受失败：服务器响应异常')
        return false
      }
    } catch (error) {
      console.error('接受任务失败:', error)
      const errorMessage = error.response?.data?.message || error.message || '接受任务失败'
      ElMessage.error(errorMessage)
      return false
    }
  }

  /**
   * 提交任务完成
   */
  const submitTaskCompletion = async (taskId, completionData) => {
    try {
      const response = await taskAPI.submitTaskCompletion(taskId, completionData)
      
      if (response && response.data) {
        // 重新加载数据以获取最新状态
        await Promise.all([
          loadBindingInfo(), // 更新统计数据
          loadMyTasks()      // 更新任务状态
        ])

        // 触发全局数据更新事件
        window.dispatchEvent(new CustomEvent('tasks-updated', {
          detail: myTasks.value
        }))

        window.dispatchEvent(new CustomEvent('student-stats-updated', {
          detail: { binding: bindingInfo.value, stats: taskStats.value }
        }))

        ElMessage.success('任务提交成功')
        return true
      }
    } catch (error) {
      console.error('提交任务完成失败:', error)
      ElMessage.error('提交失败，请重试')
      return false
    }
  }

  /**
   * 刷新所有数据
   */
  const refreshAllData = async () => {
    await initStudentData()
  }

  /**
   * 清理数据（用于登出）
   */
  const clearData = () => {
    bindingInfo.value = null
    taskStats.value = {
      accepted_tasks: 0,
      completed_tasks: 0,
      total_points: 0,
      completion_rate: 0,
      total_study_time: 0
    }
    myTasks.value = []
    availableTasks.value = []
    lastUpdateTime.value = null
    console.log('学生数据已清理')
  }

  /**
   * 获取任务详情
   */
  const getTaskById = (taskId) => {
    return myTasks.value.find(task => task.id === taskId) || 
           availableTasks.value.find(task => task.id === taskId)
  }

  return {
    // 状态
    isLoading,
    lastUpdateTime,
    bindingInfo,
    taskStats,
    myTasks,
    availableTasks,
    
    // 计算属性
    isAuthenticated,
    hasBinding,
    supervisorInfo,
    todayPoints,
    weeklyCompletedTasks,
    studyHoursToday,
    
    // 方法
    initStudentData,
    loadBindingInfo,
    loadMyTasks,
    loadAvailableTasks,
    acceptTask,
    submitTaskCompletion,
    refreshAllData,
    clearData,
    getTaskById
  }
})
