const db = require('../config/database');
const { queryDocuments, createDocument, updateDocument, deleteDocument } = require('../utils/modelHelper');

class Binding {
  constructor(data) {
    this.id = data.id || data._id;
    this.supervisor_id = data.supervisor_id;
    this.student_id = data.student_id;
    this.bind_code = data.bind_code;
    this.status = data.status || 'active';
    this.created_at = data.created_at || new Date();
    this.updated_at = data.updated_at || new Date();
  }

  // 生成随机绑定码
  static generateBindCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // 创建绑定关系
  static async createBinding(data) {
    try {
      // 检查监督者是否已有绑定码
      const existingBinding = await this.getBindingBySupervisor(data.supervisor_id);
      if (existingBinding) {
        throw new Error('监督者已有绑定关系');
      }

      // 生成唯一绑定码
      let bindCode;
      let isUnique = false;
      let attempts = 0;
      
      while (!isUnique && attempts < 10) {
        bindCode = this.generateBindCode();
        const existing = await this.getBindingByCode(bindCode);
        if (!existing) {
          isUnique = true;
        }
        attempts++;
      }

      if (!isUnique) {
        throw new Error('无法生成唯一绑定码');
      }

      const bindingData = {
        supervisor_id: data.supervisor_id,
        student_id: data.student_id || null,
        bind_code: bindCode,
        status: 'pending', // pending: 等待绑定, active: 已绑定, inactive: 已解除
        created_at: new Date(),
        updated_at: new Date()
      };

      const result = await createDocument(db, 'bindings', bindingData);
      return new Binding({ ...bindingData, id: result.id });
    } catch (error) {
      console.error('创建绑定关系失败:', error);
      throw error;
    }
  }

  // 确认绑定（学生输入绑定码）
  static async confirmBinding(studentId, bindCode) {
    try {
      // 查找绑定码
      const binding = await this.getBindingByCode(bindCode);
      if (!binding) {
        throw new Error('绑定码不存在或已过期');
      }

      if (binding.status !== 'pending') {
        throw new Error('绑定码已被使用');
      }

      // 检查学生是否已有绑定关系
      const existingStudentBinding = await this.getBindingByStudent(studentId);
      if (existingStudentBinding) {
        throw new Error('学生已有绑定关系');
      }

      // 更新绑定关系
      const updateData = {
        student_id: studentId,
        status: 'active',
        updated_at: new Date()
      };

      await updateDocument(db, 'bindings', binding.id, updateData);
      
      // 返回更新后的绑定信息
      return new Binding({ ...binding, ...updateData });
    } catch (error) {
      console.error('确认绑定失败:', error);
      throw error;
    }
  }

  // 根据绑定码查找
  static async getBindingByCode(bindCode) {
    try {
      const results = await queryDocuments(db, 'bindings', { bind_code: bindCode });
      return results.length > 0 ? new Binding(results[0]) : null;
    } catch (error) {
      console.error('查找绑定码失败:', error);
      throw error;
    }
  }

  // 根据监督者ID查找
  static async getBindingBySupervisor(supervisorId) {
    try {


      const results = await queryDocuments(db, 'bindings', {
        supervisor_id: supervisorId,
        status: { $in: ['pending', 'active'] }
      });
      return results.length > 0 ? new Binding(results[0]) : null;
    } catch (error) {
      console.error('查找监督者绑定失败:', error);
      throw error;
    }
  }

  // 根据学生ID查找
  static async getBindingByStudent(studentId) {
    try {
      const results = await queryDocuments(db, 'bindings', {
        student_id: studentId,
        status: 'active'
      });
      return results.length > 0 ? new Binding(results[0]) : null;
    } catch (error) {
      console.error('查找学生绑定失败:', error);
      throw error;
    }
  }

  // 获取绑定详情（包含用户信息）
  static async getBindingWithUsers(bindingId) {
    try {
      const User = require('./User');
      const binding = await this.getBindingById(bindingId);
      
      if (!binding) {
        return null;
      }

      // 获取监督者信息
      const supervisor = await User.getUserById(binding.supervisor_id);
      
      // 获取学生信息（如果已绑定）
      let student = null;
      if (binding.student_id) {
        student = await User.getUserById(binding.student_id);
      }

      return {
        ...binding,
        supervisor: supervisor ? {
          id: supervisor.id,
          username: supervisor.username,
          email: supervisor.email
        } : null,
        student: student ? {
          id: student.id,
          username: student.username,
          email: student.email
        } : null
      };
    } catch (error) {
      console.error('获取绑定详情失败:', error);
      throw error;
    }
  }

  // 根据ID查找绑定
  static async getBindingById(bindingId) {
    try {
      const results = await queryDocuments(db, 'bindings', { _id: bindingId });
      return results.length > 0 ? new Binding(results[0]) : null;
    } catch (error) {
      console.error('查找绑定失败:', error);
      throw error;
    }
  }

  // 解除绑定
  static async unbind(bindingId) {
    try {
      const updateData = {
        status: 'inactive',
        updated_at: new Date()
      };

      await updateDocument(db, 'bindings', bindingId, updateData);
      return true;
    } catch (error) {
      console.error('解除绑定失败:', error);
      throw error;
    }
  }

  // 获取监督者的所有学生
  static async getStudentsBySupervisor(supervisorId) {
    try {
      const User = require('./User');
      const results = await queryDocuments(db, 'bindings', {
        supervisor_id: supervisorId,
        status: 'active'
      });

      const students = [];
      for (const binding of results) {
        if (binding.student_id) {
          const student = await User.getUserById(binding.student_id);
          if (student) {
            students.push({
              binding_id: binding._id,
              student: {
                id: student.id,
                username: student.username,
                email: student.email
              },
              created_at: binding.created_at
            });
          }
        }
      }

      return students;
    } catch (error) {
      console.error('获取学生列表失败:', error);
      throw error;
    }
  }

  // {{ AURA-X: Add - 添加删除绑定方法. Approval: 寸止(ID:1734683400). }}
  // 删除绑定关系（物理删除）
  static async deleteBinding(bindingId) {
    try {
      await deleteDocument(db, 'bindings', bindingId);
      return true;
    } catch (error) {
      console.error('删除绑定关系失败:', error);
      throw error;
    }
  }

  // 获取监督者的所有绑定关系（包括inactive）
  static async getAllBindingsBySupervisor(supervisorId) {
    try {
      const User = require('./User');
      const results = await queryDocuments(db, 'bindings', {
        supervisor_id: supervisorId
      });

      const bindings = [];
      for (const binding of results) {
        if (binding.student_id) {
          const student = await User.getUserById(binding.student_id);
          if (student) {
            bindings.push({
              binding_id: binding._id,
              student: {
                id: student.id,
                username: student.username,
                email: student.email
              },
              status: binding.status,
              created_at: binding.created_at
            });
          }
        }
      }

      return bindings;
    } catch (error) {
      console.error('获取所有绑定关系失败:', error);
      throw error;
    }
  }

  // 获取所有绑定关系
  static async getAllBindings() {
    try {
      return await queryDocuments(db, 'bindings', {});
    } catch (error) {
      console.error('获取所有绑定关系失败:', error);
      throw error;
    }
  }
}

module.exports = Binding;
