"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.newDb = void 0;
const database_1 = require("@cloudbase/database");
const utils_1 = require("../utils/utils");
const code_1 = require("../const/code");
const tcbdbapirequester_1 = require("../utils/tcbdbapirequester");
function newDb(cloudbase, dbConfig = {}) {
    database_1.Db.reqClass = tcbdbapirequester_1.TcbDBApiHttpRequester;
    // 兼容方法预处理
    if (Object.prototype.toString.call(dbConfig).slice(8, -1) !== 'Object') {
        throw (0, utils_1.E)(Object.assign(Object.assign({}, code_1.ERROR.INVALID_PARAM), { message: 'dbConfig must be an object' }));
    }
    if (dbConfig === null || dbConfig === void 0 ? void 0 : dbConfig.env) {
        // env变量名转换
        dbConfig.envName = dbConfig.env;
        delete dbConfig.env;
    }
    return new database_1.Db(Object.assign(Object.assign({}, cloudbase.config), dbConfig));
}
exports.newDb = newDb;
