<template>
  <div class="statistics-page">
    <h1>数据统计</h1>
    <p>查看考研者的学习数据和进度统计</p>
    
    <div class="stats-cards">
      <el-card class="stat-card">
        <template #header>
          <div class="card-header">
            <span>任务完成情况</span>
            <el-tag>本周</el-tag>
          </div>
        </template>
        <div class="stat-content">
          <div class="stat-number">{{ stats.completedTasks }} / {{ stats.totalTasks }}</div>
          <div class="stat-label">已完成 / 总任务</div>
          <el-progress 
            :percentage="stats.completionRate" 
            :color="getProgressColor(stats.completionRate)"
          />
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <template #header>
          <div class="card-header">
            <span>学习时长</span>
            <el-tag type="success">本周</el-tag>
          </div>
        </template>
        <div class="stat-content">
          <div class="stat-number">{{ stats.studyHours }}小时</div>
          <div class="stat-label">总学习时长</div>
          <div class="stat-sub">日均: {{ stats.dailyAverage }}小时</div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <template #header>
          <div class="card-header">
            <span>积分情况</span>
            <el-tag type="warning">本月</el-tag>
          </div>
        </template>
        <div class="stat-content">
          <div class="stat-number">{{ stats.totalPoints }}</div>
          <div class="stat-label">累计积分</div>
          <div class="stat-sub">本周获得: +{{ stats.weeklyPoints }}</div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <template #header>
          <div class="card-header">
            <span>备考进度</span>
            <el-tag type="danger">总体</el-tag>
          </div>
        </template>
        <div class="stat-content">
          <div class="stat-number">{{ stats.examProgress }}%</div>
          <div class="stat-label">总体进度</div>
          <div class="stat-sub">距离考试: {{ stats.daysToExam }}天</div>
        </div>
      </el-card>
    </div>
    
    <div class="chart-section">
      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>每日任务完成情况</span>
            <el-select v-model="timeRange" placeholder="时间范围" size="small">
              <el-option label="最近7天" value="7days" />
              <el-option label="最近30天" value="30days" />
              <el-option label="本月" value="month" />
            </el-select>
          </div>
        </template>
        <div class="chart-container">
          <div class="chart-placeholder">
            <div class="bar-chart">
              <div v-for="(day, index) in weeklyData" :key="index" class="chart-bar-container">
                <div class="chart-bar-wrapper">
                  <div class="chart-bar completed" :style="{ height: `${day.completedPercentage}%` }"></div>
                  <div class="chart-bar failed" :style="{ height: `${day.failedPercentage}%` }"></div>
                </div>
                <div class="chart-label">{{ day.label }}</div>
              </div>
            </div>
            <div class="chart-legend">
              <div class="legend-item">
                <div class="legend-color completed"></div>
                <span>已完成</span>
              </div>
              <div class="legend-item">
                <div class="legend-color failed"></div>
                <span>未完成</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
      
      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>学科分布</span>
          </div>
        </template>
        <div class="chart-container">
          <div class="pie-chart-placeholder">
            <div class="pie-segments">
              <div class="pie-segment" style="--percentage: 35%; --color: #409eff; --rotation: 0deg;"></div>
              <div class="pie-segment" style="--percentage: 25%; --color: #67c23a; --rotation: 126deg;"></div>
              <div class="pie-segment" style="--percentage: 20%; --color: #e6a23c; --rotation: 216deg;"></div>
              <div class="pie-segment" style="--percentage: 20%; --color: #f56c6c; --rotation: 288deg;"></div>
            </div>
            <div class="pie-legend">
              <div class="legend-item">
                <div class="legend-color" style="background-color: #409eff;"></div>
                <span>数学 (35%)</span>
              </div>
              <div class="legend-item">
                <div class="legend-color" style="background-color: #67c23a;"></div>
                <span>英语 (25%)</span>
              </div>
              <div class="legend-item">
                <div class="legend-color" style="background-color: #e6a23c;"></div>
                <span>政治 (20%)</span>
              </div>
              <div class="legend-item">
                <div class="legend-color" style="background-color: #f56c6c;"></div>
                <span>专业课 (20%)</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
    
    <div class="table-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>详细数据</span>
            <el-button type="primary" size="small" @click="exportData">导出数据</el-button>
          </div>
        </template>
        <el-table :data="detailedData" style="width: 100%" height="400">
          <el-table-column prop="date" label="日期" width="120" />
          <el-table-column prop="totalTasks" label="总任务数" width="100" />
          <el-table-column prop="completedTasks" label="已完成" width="100" />
          <el-table-column prop="completionRate" label="完成率" width="100">
            <template #default="scope">
              {{ scope.row.completionRate }}%
            </template>
          </el-table-column>
          <el-table-column prop="studyHours" label="学习时长(小时)" width="140" />
          <el-table-column prop="points" label="获得积分" width="100" />
          <el-table-column prop="notes" label="备注" />
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { bindingAPI } from '@/api/binding';
import { useAuthStore } from '@/stores/auth';

// 时间范围选择
const timeRange = ref('7days');

// 统计数据
const stats = ref({
  completedTasks: 0,
  totalTasks: 0,
  completionRate: 0,
  studyHours: 0,
  dailyAverage: 0,
  totalPoints: 0,
  weeklyPoints: 0,
  examProgress: 0,
  daysToExam: 120
});

// 每周数据
const weeklyData = ref([
  { label: '周一', completedPercentage: 0, failedPercentage: 0 },
  { label: '周二', completedPercentage: 0, failedPercentage: 0 },
  { label: '周三', completedPercentage: 0, failedPercentage: 0 },
  { label: '周四', completedPercentage: 0, failedPercentage: 0 },
  { label: '周五', completedPercentage: 0, failedPercentage: 0 },
  { label: '周六', completedPercentage: 0, failedPercentage: 0 },
  { label: '周日', completedPercentage: 0, failedPercentage: 0 }
]);

// 详细数据
const detailedData = ref([]);

// 科目数据
const subjectData = ref([]);

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage < 30) return '#909399';
  if (percentage < 70) return '#e6a23c';
  if (percentage < 90) return '#409eff';
  return '#67c23a';
};

// 加载统计数据
const loadStatistics = async () => {
  try {
    const authStore = useAuthStore();
    const currentUser = authStore.user;

    if (!currentUser) {
      console.log('用户未登录');
      return;
    }

    // 获取学生列表（包含任务统计）
    const response = await bindingAPI.getStudentList({ supervisor_id: currentUser.id });
    if (response && response.data && response.data.length > 0) {
      const student = response.data[0]; // 获取第一个学生
      const taskStats = student.task_stats;

      if (taskStats) {
        // 更新统计数据
        stats.value = {
          completedTasks: taskStats.completed_tasks || 0,
          totalTasks: taskStats.accepted_tasks || 0,
          completionRate: taskStats.completion_rate || 0,
          studyHours: Math.round((taskStats.total_study_time || 0) / 60 * 10) / 10, // 转换为小时
          dailyAverage: Math.round((taskStats.total_study_time || 0) / 7 / 60 * 10) / 10, // 每日平均小时
          totalPoints: taskStats.total_points || 0,
          weeklyPoints: taskStats.total_points || 0, // 暂时使用总积分
          examProgress: Math.min(taskStats.completion_rate || 0, 100),
          daysToExam: 120 // 固定值
        };

        console.log('统计数据已更新:', stats.value);
      } else {
        console.log('没有找到任务统计数据');
      }
    } else {
      console.log('没有找到绑定的学生');
    }
  } catch (error) {
    console.error('加载统计数据失败:', error);
    ElMessage.error('加载统计数据失败');
  }
};

// 导出数据
const exportData = () => {
  ElMessage.success('数据导出成功');
};

// 页面初始化
onMounted(() => {
  console.log('=== Statistics页面onMounted触发 ===');
  loadStatistics();
});

// 暴露函数到window对象用于调试
if (typeof window !== 'undefined') {
  window.loadStatistics = loadStatistics;
  window.statsRef = stats;
}
</script>

<style scoped>
.statistics-page {
  padding: 20px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-content {
  text-align: center;
  padding: 10px 0;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.stat-sub {
  font-size: 12px;
  color: #606266;
  margin-top: 5px;
}

.chart-section {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.bar-chart {
  flex: 1;
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  padding: 0 20px 30px 20px;
}

.chart-bar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 40px;
}

.chart-bar-wrapper {
  width: 100%;
  height: 200px;
  display: flex;
  flex-direction: column-reverse;
  justify-content: flex-start;
}

.chart-bar {
  width: 100%;
  border-radius: 4px 4px 0 0;
}

.chart-bar.completed {
  background-color: #67c23a;
}

.chart-bar.failed {
  background-color: #f56c6c;
  margin-top: 1px;
}

.chart-label {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #606266;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.completed {
  background-color: #67c23a;
}

.legend-color.failed {
  background-color: #f56c6c;
}

.pie-chart-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.pie-segments {
  position: relative;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  overflow: hidden;
}

.pie-segment {
  position: absolute;
  width: 100%;
  height: 100%;
  transform-origin: 50% 50%;
  transform: rotate(var(--rotation));
}

.pie-segment::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: conic-gradient(
    var(--color) 0% var(--percentage),
    transparent var(--percentage) 100%
  );
}

.pie-legend {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.table-section {
  margin-bottom: 20px;
}

@media (max-width: 1200px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .chart-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }
}
</style>