<template>
  <div class="tasks-page">
    <h1>监督者任务管理</h1>
    <p>此页面用于监督者管理考研者的任务</p>
    
    <div class="action-bar">
      <el-button type="primary" @click="$router.push('/supervisor/create-task')">
        <el-icon><Plus /></el-icon>
        创建新任务
      </el-button>
      
      <div class="filters">
        <el-select v-model="filterStatus" placeholder="任务状态" size="default">
          <el-option label="全部状态" value="" />
          <el-option label="待完成" value="pending" />
          <el-option label="已完成" value="completed" />
          <el-option label="已失败" value="failed" />
        </el-select>
        
        <el-select v-model="filterType" placeholder="任务类型" size="default">
          <el-option label="全部类型" value="" />
          <el-option label="每日任务" value="daily" />
          <el-option label="周常任务" value="weekly" />
          <el-option label="专项任务" value="special" />
        </el-select>
      </div>
    </div>
    
    <el-table :data="filteredTasks" style="width: 100%" v-loading="loading">
      <el-table-column prop="title" label="任务名称" min-width="200">
        <template #default="scope">
          <div class="task-title">
            <span :class="getTaskTypeClass(scope.row.type)">
              {{ getTaskTypeLabel(scope.row.type) }}
            </span>
            {{ scope.row.title }}
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="deadline" label="截止日期" width="120">
        <template #default="scope">
          {{ formatDate(scope.row.deadline) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusLabel(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="points" label="积分" width="80" />
      
      <el-table-column prop="acceptanceRate" label="领取情况" width="120">
        <template #default="scope">
          {{ scope.row.acceptedBy?.length || 0 }} / {{ totalStudents.value }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button size="small" @click="viewTask(scope.row)">查看</el-button>
          <el-button size="small" type="primary" @click="editTask(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteTask(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="totalTasks"
        :page-size="pageSize"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';

const router = useRouter();
const loading = ref(false);
const filterStatus = ref('');
const filterType = ref('');
const currentPage = ref(1);
const pageSize = ref(10);

// 任务数据
const tasks = ref([]);
const totalStudents = ref(20); // 模拟总学生数

// 加载任务数据
const loadTasks = () => {
  loading.value = true;
  
  try {
    // 从localStorage加载任务数据
    const storedTasks = JSON.parse(localStorage.getItem('globalTasks') || '[]');
    
    if (storedTasks.length > 0) {
      tasks.value = storedTasks;
    } else {
      // 如果没有存储的任务，使用默认任务
      tasks.value = [
        {
          id: 1,
          title: "完成数学高数第三章习题",
          description: "完成《高等数学》第三章微分方程的全部习题，并提交解答过程。",
          type: "daily",
          status: "pending",
          deadline: "2025-08-20",
          points: 30,
          subject: "数学",
          difficulty: 2,
          acceptedBy: []
        },
        {
          id: 2,
          title: "英语阅读理解训练",
          description: "完成5篇考研英语阅读理解训练，并分析文章结构和答题思路。",
          type: "weekly",
          status: "completed",
          deadline: "2025-08-25",
          points: 50,
          subject: "英语",
          difficulty: 2,
          acceptedBy: []
        },
        {
          id: 3,
          title: "专业课模拟测试",
          description: "完成一套完整的专业课模拟测试，时间控制在3小时内。",
          type: "special",
          status: "failed",
          deadline: "2025-09-01",
          points: 100,
          subject: "专业课",
          difficulty: 3,
          acceptedBy: []
        }
      ];
      
      // 保存默认任务到localStorage
      localStorage.setItem('globalTasks', JSON.stringify(tasks.value));
    }
  } catch (error) {
    console.error('加载任务数据失败:', error);
    ElMessage.error('加载任务数据失败');
  } finally {
    loading.value = false;
  }
};

// 在组件挂载时加载任务
onMounted(() => {
  loadTasks();
});

// 根据筛选条件过滤任务
const filteredTasks = computed(() => {
  let result = [...tasks.value];
  
  // 按状态筛选
  if (filterStatus.value) {
    result = result.filter(task => task.status === filterStatus.value);
  }
  
  // 按类型筛选
  if (filterType.value) {
    result = result.filter(task => task.type === filterType.value);
  }
  
  return result;
});

// 总任务数
const totalTasks = computed(() => filteredTasks.value.length);

// 获取任务类型标签
const getTaskTypeLabel = (type) => {
  switch (type) {
    case 'daily': return '每日任务';
    case 'weekly': return '周常任务';
    case 'special': return '专项任务';
    default: return '未知类型';
  }
};

// 获取任务类型样式类
const getTaskTypeClass = (type) => {
  switch (type) {
    case 'daily': return 'task-type daily';
    case 'weekly': return 'task-type weekly';
    case 'special': return 'task-type special';
    default: return 'task-type';
  }
};

// 获取状态标签
const getStatusLabel = (status) => {
  switch (status) {
    case 'pending': return '待完成';
    case 'completed': return '已完成';
    case 'failed': return '已失败';
    default: return '未知状态';
  }
};

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'pending': return 'info';
    case 'completed': return 'success';
    case 'failed': return 'danger';
    default: return '';
  }
};

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 查看任务
const viewTask = (task) => {
  router.push(`/supervisor/task/${task.id}`);
};

// 编辑任务
const editTask = (task) => {
  router.push(`/supervisor/create-task?id=${task.id}`);
};

// 删除任务
const deleteTask = (task) => {
  ElMessageBox.confirm(
    '确定要删除这个任务吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    try {
      // 从内存中删除
      const index = tasks.value.findIndex(t => t.id === task.id);
      if (index !== -1) {
        tasks.value.splice(index, 1);
      }
      
      // 从localStorage中删除
      const storedTasks = JSON.parse(localStorage.getItem('globalTasks') || '[]');
      const storedIndex = storedTasks.findIndex(t => t.id === task.id);
      if (storedIndex !== -1) {
        storedTasks.splice(storedIndex, 1);
        localStorage.setItem('globalTasks', JSON.stringify(storedTasks));
      }
      
      ElMessage.success('任务删除成功');
    } catch (error) {
      console.error('删除任务失败:', error);
      ElMessage.error('删除任务失败');
    }
  }).catch(() => {
    // 取消操作
  });
};

// 处理页码变化
const handlePageChange = (page) => {
  currentPage.value = page;
};
</script>

<style scoped>
.tasks-page {
  padding: 20px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.filters {
  display: flex;
  gap: 10px;
}

.task-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-type {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  color: white;
}

.task-type.daily {
  background-color: #409eff;
}

.task-type.weekly {
  background-color: #67c23a;
}

.task-type.special {
  background-color: #e6a23c;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>