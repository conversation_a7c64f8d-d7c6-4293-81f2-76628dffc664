// {{ AURA-X: Add - 创建数据同步状态管理. Approval: 寸止(ID:1734682800). }}
// 数据同步状态管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import realtimeService from '@/services/realtime'
import { useAuthStore } from './auth'

export const useSyncStore = defineStore('sync', () => {
  // 状态
  const isInitialized = ref(false)
  const syncStatus = ref('disconnected') // disconnected, connecting, connected, error
  const lastSyncTime = ref(null)
  const subscriptions = ref(new Set())
  
  // 数据缓存
  const tasksCache = ref(new Map())
  const bindingsCache = ref(new Map())
  const pointsCache = ref(new Map())
  const usersCache = ref(new Map())

  // 计算属性
  const isConnected = computed(() => syncStatus.value === 'connected')
  const hasActiveSubscriptions = computed(() => subscriptions.value.size > 0)

  /**
   * 初始化同步服务
   */
  const initSync = async () => {
    if (isInitialized.value) {
      console.log('同步服务已初始化')
      return
    }

    try {
      syncStatus.value = 'connecting'
      
      // 初始化实时服务
      await realtimeService.init({
        // CloudBase配置可以从环境变量或配置文件获取
        envId: import.meta.env.VITE_CLOUDBASE_ENV_ID,
        region: 'ap-shanghai'
      })

      // 监听连接状态变化
      watchConnectionStatus()
      
      isInitialized.value = true
      syncStatus.value = 'connected'
      lastSyncTime.value = new Date()
      
      console.log('数据同步服务初始化成功')
      
    } catch (error) {
      console.error('同步服务初始化失败:', error)
      syncStatus.value = 'error'
      ElMessage.error('数据同步初始化失败')
    }
  }

  /**
   * 监听连接状态
   */
  const watchConnectionStatus = () => {
    // 这里可以添加对realtimeService状态的监听
    setInterval(() => {
      const status = realtimeService.getConnectionStatus()
      syncStatus.value = status.isConnected ? 'connected' : 'disconnected'
    }, 5000)
  }

  /**
   * 订阅任务数据变更
   */
  const subscribeToTasks = (userId, userRole) => {
    const query = userRole === 'student' 
      ? { studentId: userId }
      : { creatorId: userId }
    
    const subscriptionKey = realtimeService.subscribe(
      'tasks',
      query,
      (docs, changes) => {
        handleTasksUpdate(docs, changes)
      }
    )
    
    subscriptions.value.add(subscriptionKey)
    console.log(`已订阅任务数据: ${subscriptionKey}`)
    return subscriptionKey
  }

  /**
   * 订阅绑定关系变更
   */
  const subscribeToBindings = (userId, userRole) => {
    const query = userRole === 'supervisor'
      ? { supervisor_id: userId }
      : { student_id: userId }
    
    const subscriptionKey = realtimeService.subscribe(
      'bindings',
      query,
      (docs, changes) => {
        handleBindingsUpdate(docs, changes)
      }
    )
    
    subscriptions.value.add(subscriptionKey)
    console.log(`已订阅绑定数据: ${subscriptionKey}`)
    return subscriptionKey
  }

  /**
   * 订阅积分变更
   */
  const subscribeToPoints = (userId) => {
    const subscriptionKey = realtimeService.subscribe(
      'points',
      { userId },
      (docs, changes) => {
        handlePointsUpdate(docs, changes)
      }
    )
    
    subscriptions.value.add(subscriptionKey)
    console.log(`已订阅积分数据: ${subscriptionKey}`)
    return subscriptionKey
  }

  /**
   * 处理任务数据更新
   */
  const handleTasksUpdate = (docs, changes) => {
    console.log('任务数据实时更新:', { docs, changes })
    
    // 更新缓存
    docs.forEach(doc => {
      tasksCache.value.set(doc._id, doc)
    })
    
    // 处理变更类型
    changes.forEach(change => {
      switch (change.type) {
        case 'added':
          ElMessage.success('有新任务发布')
          break
        case 'modified':
          ElMessage.info('任务信息已更新')
          break
        case 'removed':
          tasksCache.value.delete(change.doc._id)
          ElMessage.warning('任务已被删除')
          break
      }
    })
    
    // 触发自定义事件，通知组件更新
    window.dispatchEvent(new CustomEvent('tasks-updated', {
      detail: { docs, changes }
    }))
    
    lastSyncTime.value = new Date()
  }

  /**
   * 处理绑定关系更新
   */
  const handleBindingsUpdate = (docs, changes) => {
    console.log('绑定关系实时更新:', { docs, changes })
    
    // 更新缓存
    docs.forEach(doc => {
      bindingsCache.value.set(doc._id, doc)
    })
    
    // 处理变更
    changes.forEach(change => {
      switch (change.type) {
        case 'added':
          ElMessage.success('新的绑定关系建立')
          break
        case 'modified':
          ElMessage.info('绑定状态已更新')
          break
        case 'removed':
          bindingsCache.value.delete(change.doc._id)
          ElMessage.warning('绑定关系已解除')
          break
      }
    })
    
    // 触发事件
    window.dispatchEvent(new CustomEvent('bindings-updated', {
      detail: { docs, changes }
    }))
    
    lastSyncTime.value = new Date()
  }

  /**
   * 处理积分更新
   */
  const handlePointsUpdate = (docs, changes) => {
    console.log('积分数据实时更新:', { docs, changes })
    
    // 更新缓存
    docs.forEach(doc => {
      pointsCache.value.set(doc._id, doc)
    })
    
    // 处理变更
    changes.forEach(change => {
      if (change.type === 'added' && change.doc.type === 'earned') {
        ElMessage.success(`获得 ${change.doc.amount} 积分`)
      }
    })
    
    // 触发事件
    window.dispatchEvent(new CustomEvent('points-updated', {
      detail: { docs, changes }
    }))
    
    lastSyncTime.value = new Date()
  }

  /**
   * 启动用户相关的所有订阅
   */
  const startUserSubscriptions = () => {
    const authStore = useAuthStore()
    const user = authStore.user
    
    if (!user) {
      console.warn('用户未登录，无法启动订阅')
      return
    }

    console.log(`为用户 ${user.username} (${user.role}) 启动实时订阅`)
    
    // 订阅任务数据
    subscribeToTasks(user.id, user.role)
    
    // 订阅绑定关系
    subscribeToBindings(user.id, user.role)
    
    // 订阅积分数据
    subscribeToPoints(user.id)
  }

  /**
   * 停止所有订阅
   */
  const stopAllSubscriptions = () => {
    subscriptions.value.forEach(key => {
      realtimeService.unsubscribe(key)
    })
    subscriptions.value.clear()
    console.log('已停止所有实时订阅')
  }

  /**
   * 获取缓存数据
   */
  const getCachedTasks = () => Array.from(tasksCache.value.values())
  const getCachedBindings = () => Array.from(bindingsCache.value.values())
  const getCachedPoints = () => Array.from(pointsCache.value.values())

  /**
   * 清理缓存
   */
  const clearCache = () => {
    tasksCache.value.clear()
    bindingsCache.value.clear()
    pointsCache.value.clear()
    usersCache.value.clear()
    console.log('已清理所有缓存数据')
  }

  /**
   * 销毁同步服务
   */
  const destroySync = () => {
    stopAllSubscriptions()
    clearCache()
    realtimeService.destroy()
    isInitialized.value = false
    syncStatus.value = 'disconnected'
    console.log('数据同步服务已销毁')
  }

  /**
   * 通用的集合订阅方法
   * @param {string} collection - 集合名称
   * @param {Object} query - 查询条件
   * @param {Function} callback - 变更回调
   */
  const subscribeToCollection = async (collection, query = {}, callback) => {
    if (!isInitialized.value) {
      await initSync()
    }

    try {
      const subscriptionKey = realtimeService.subscribe(collection, query, callback)
      subscriptions.value.add(subscriptionKey)

      console.log(`已订阅集合 ${collection}`)
      return subscriptionKey

    } catch (error) {
      console.error(`订阅集合 ${collection} 失败:`, error)
      throw error
    }
  }

  return {
    // 状态
    isInitialized,
    syncStatus,
    lastSyncTime,
    subscriptions,
    
    // 计算属性
    isConnected,
    hasActiveSubscriptions,
    
    // 方法
    initSync,
    startUserSubscriptions,
    stopAllSubscriptions,
    getCachedTasks,
    getCachedBindings,
    getCachedPoints,
    clearCache,
    destroySync,
    
    // 单独订阅方法
    subscribeToTasks,
    subscribeToBindings,
    subscribeToPoints,
    subscribeToCollection
  }
})
