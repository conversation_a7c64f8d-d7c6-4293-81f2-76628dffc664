const db = require('../config/database');
const { createDocument, getDocumentById, deleteDocument, queryDocuments } = require('../utils/modelHelper');

// 集合名称
const COLLECTION = 'points';

/**
 * 创建积分记录
 * @param {Object} pointData - 积分记录数据
 * @returns {Promise} - 返回创建的积分记录
 */
const createPointRecord = async (pointData) => {
  try {
    return await createDocument(db, COLLECTION, {
      userId: pointData.userId,
      amount: pointData.amount,
      type: pointData.type, // 'earned' 或 'spent'
      source: pointData.source,
      description: pointData.description || '',
      relatedId: pointData.relatedId || null,
      relatedType: pointData.relatedType || null
    });
  } catch (error) {
    console.error('创建积分记录失败:', error);
    throw error;
  }
};

/**
 * 根据ID获取积分记录
 * @param {String} id - 积分记录ID
 * @returns {Promise} - 返回积分记录数据
 */
const getPointRecordById = async (id) => {
  try {
    return await getDocumentById(db, COLLECTION, id);
  } catch (error) {
    console.error('获取积分记录失败:', error);
    throw error;
  }
};

/**
 * 获取用户的积分记录
 * @param {String} userId - 用户ID
 * @param {Object} options - 查询选项
 * @returns {Promise} - 返回积分记录列表
 */
const getUserPointRecords = async (userId, options = {}) => {
  try {
    // 默认按创建时间降序排序
    const defaultOptions = {
      orderBy: { field: 'createdAt', direction: 'desc' },
      ...options
    };
    
    return await queryDocuments(db, COLLECTION, { userId }, defaultOptions);
  } catch (error) {
    console.error('获取用户积分记录失败:', error);
    throw error;
  }
};

/**
 * 获取用户的积分总和
 * @param {String} userId - 用户ID
 * @returns {Promise} - 返回积分总和
 */
const getUserTotalPoints = async (userId) => {
  try {
    const records = await getUserPointRecords(userId);
    return records.reduce((total, record) => total + record.amount, 0);
  } catch (error) {
    console.error('获取用户积分总和失败:', error);
    throw error;
  }
};

/**
 * 记录任务完成奖励积分
 * @param {String} userId - 用户ID
 * @param {Number} points - 奖励积分
 * @param {String} taskId - 任务ID
 * @param {String} taskTitle - 任务标题
 * @returns {Promise} - 返回创建的积分记录
 */
const recordTaskCompletionPoints = async (userId, points, taskId, taskTitle) => {
  try {
    return await createPointRecord({
      userId,
      amount: points,
      type: 'earned',
      source: 'task_completion',
      description: `完成任务：${taskTitle}`,
      relatedId: taskId,
      relatedType: 'task'
    });
  } catch (error) {
    console.error('记录任务完成积分失败:', error);
    throw error;
  }
};

/**
 * 记录奖励兑换消费积分
 * @param {String} userId - 用户ID
 * @param {Number} points - 消费积分
 * @param {String} rewardId - 奖励ID
 * @param {String} rewardName - 奖励名称
 * @returns {Promise} - 返回创建的积分记录
 */
const recordRewardRedemptionPoints = async (userId, points, rewardId, rewardName) => {
  try {
    return await createPointRecord({
      userId,
      amount: -Math.abs(points), // 确保是负数
      type: 'spent',
      source: 'reward_redemption',
      description: `兑换奖励：${rewardName}`,
      relatedId: rewardId,
      relatedType: 'reward'
    });
  } catch (error) {
    console.error('记录奖励兑换积分失败:', error);
    throw error;
  }
};

/**
 * 记录管理员调整积分
 * @param {String} userId - 用户ID
 * @param {Number} points - 调整积分
 * @param {String} reason - 调整原因
 * @param {String} adminId - 管理员ID
 * @returns {Promise} - 返回创建的积分记录
 */
const recordAdminAdjustment = async (userId, points, reason, adminId) => {
  try {
    return await createPointRecord({
      userId,
      amount: points,
      type: points > 0 ? 'earned' : 'spent',
      source: 'admin_adjustment',
      description: reason || '管理员调整',
      relatedId: adminId,
      relatedType: 'admin'
    });
  } catch (error) {
    console.error('记录管理员调整积分失败:', error);
    throw error;
  }
};

/**
 * 根据用户ID获取积分记录（简化版）
 * @param {String} userId - 用户ID
 * @returns {Promise} - 返回用户的所有积分记录
 */
const getPointsByUser = async (userId) => {
  try {
    return await queryDocuments(db, COLLECTION, { userId });
  } catch (error) {
    console.error('根据用户ID获取积分记录失败:', error);
    throw error;
  }
};

/**
 * 删除用户的所有积分记录
 * @param {String} userId - 用户ID
 * @returns {Promise} - 返回删除的记录数量
 */
const deletePointsByUser = async (userId) => {
  try {
    const points = await getPointsByUser(userId);
    let deletedCount = 0;

    for (const point of points) {
      await deleteDocument(db, COLLECTION, point._id);
      deletedCount++;
    }

    console.log(`删除用户 ${userId} 的 ${deletedCount} 个积分记录`);
    return deletedCount;
  } catch (error) {
    console.error('删除用户积分记录失败:', error);
    throw error;
  }
};

// {{ AURA-X: Add - 添加获取所有积分记录和删除方法. Approval: 寸止(ID:1734683400). }}
// 获取所有积分记录
const getAllPoints = async () => {
  try {
    return await queryDocuments(db, COLLECTION, {});
  } catch (error) {
    console.error('获取所有积分记录失败:', error);
    throw error;
  }
};

// 删除积分记录
const deletePointRecord = async (id) => {
  try {
    return await deleteDocument(db, COLLECTION, id);
  } catch (error) {
    console.error('删除积分记录失败:', error);
    throw error;
  }
};

module.exports = {
  createPointRecord,
  getPointRecordById,
  getUserPointRecords,
  getUserTotalPoints,
  recordTaskCompletionPoints,
  recordRewardRedemptionPoints,
  recordAdminAdjustment,
  getPointsByUser,
  deletePointsByUser,
  getAllPoints,
  deletePointRecord
};