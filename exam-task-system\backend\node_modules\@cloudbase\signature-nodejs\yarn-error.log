Arguments: 
  /usr/local/bin/node /usr/local/bin/yarn upgrade u@types/node

PATH: 
  /usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin

Yarn version: 
  1.22.4

Node version: 
  12.22.1

Platform: 
  darwin x64

Trace: 
  Error: https://github.com/types/node: tunneling socket could not be established, cause=connect ECONNREFUSED 127.0.0.1:12639
      at ClientRequest.onError (/usr/local/lib/node_modules/yarn/lib/cli.js:152234:17)
      at Object.onceWrapper (events.js:421:26)
      at ClientRequest.emit (events.js:314:20)
      at Socket.socketErrorListener (_http_client.js:427:9)
      at Socket.emit (events.js:314:20)
      at emitErrorNT (internal/streams/destroy.js:92:8)
      at emitErrorAndCloseNT (internal/streams/destroy.js:60:3)
      at processTicksAndRejections (internal/process/task_queues.js:84:21)

npm manifest: 
  {
    "name": "@cloudbase/signature-nodejs",
    "version": "1.1.0",
    "description": "cloudbase api signature for node.js",
    "main": "lib/index.js",
    "scripts": {
      "test": "npx jest --coverage --verbose",
      "build": "tsc"
    },
    "repository": {
      "type": "git",
      "url": "*******************:QBase/cloudbase-signature-nodejs.git"
    },
    "author": "ulyssesliu",
    "lint-staged": {
      "*.ts": [
        "eslint --fix src/**/*",
        "git add"
      ]
    },
    "dependencies": {
      "@types/clone": "^0.1.30",
      "clone": "^2.1.2",
      "is-stream": "^2.0.0",
      "url": "^0.11.0"
    },
    "devDependencies": {
      "@types/jest": "^24.0.23",
      "@types/node": "10.12.10",
      "@typescript-eslint/eslint-plugin": "^2.10.0",
      "@typescript-eslint/parser": "^2.10.0",
      "eslint": "^6.7.2",
      "eslint-plugin-typescript": "^0.14.0",
      "husky": "^3.1.0",
      "jest": "^24.9.0",
      "lint-staged": "^9.5.0",
      "ts-jest": "^24.2.0",
      "typescript": "3.5.3",
      "typescript-eslint-parser": "^22.0.0"
    }
  }

yarn manifest: 
  No manifest

Lockfile: 
  # THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
  # yarn lockfile v1
  
  
  "@babel/code-frame@^7.0.0", "@babel/code-frame@^7.5.5":
    version "7.5.5"
    resolved "http://r.tnpm.oa.com/@babel/code-frame/download/@babel/code-frame-7.5.5.tgz#bc0782f6d69f7b7d49531219699b988f669a8f9d"
    integrity sha1-vAeC9tafe31JUxIZaZuYj2aaj50=
    dependencies:
      "@babel/highlight" "^7.0.0"
  
  "@babel/core@^7.1.0":
    version "7.7.4"
    resolved "http://r.tnpm.oa.com/@babel/core/download/@babel/core-7.7.4.tgz#37e864532200cb6b50ee9a4045f5f817840166ab"
    integrity sha1-N+hkUyIAy2tQ7ppARfX4F4QBZqs=
    dependencies:
      "@babel/code-frame" "^7.5.5"
      "@babel/generator" "^7.7.4"
      "@babel/helpers" "^7.7.4"
      "@babel/parser" "^7.7.4"
      "@babel/template" "^7.7.4"
      "@babel/traverse" "^7.7.4"
      "@babel/types" "^7.7.4"
      convert-source-map "^1.7.0"
      debug "^4.1.0"
      json5 "^2.1.0"
      lodash "^4.17.13"
      resolve "^1.3.2"
      semver "^5.4.1"
      source-map "^0.5.0"
  
  "@babel/generator@^7.4.0", "@babel/generator@^7.7.4":
    version "7.7.4"
    resolved "http://r.tnpm.oa.com/@babel/generator/download/@babel/generator-7.7.4.tgz#db651e2840ca9aa66f327dcec1dc5f5fa9611369"
    integrity sha1-22UeKEDKmqZvMn3OwdxfX6lhE2k=
    dependencies:
      "@babel/types" "^7.7.4"
      jsesc "^2.5.1"
      lodash "^4.17.13"
      source-map "^0.5.0"
  
  "@babel/helper-function-name@^7.7.4":
    version "7.7.4"
    resolved "http://r.tnpm.oa.com/@babel/helper-function-name/download/@babel/helper-function-name-7.7.4.tgz#ab6e041e7135d436d8f0a3eca15de5b67a341a2e"
    integrity sha1-q24EHnE11DbY8KPsoV3ltno0Gi4=
    dependencies:
      "@babel/helper-get-function-arity" "^7.7.4"
      "@babel/template" "^7.7.4"
      "@babel/types" "^7.7.4"
  
  "@babel/helper-get-function-arity@^7.7.4":
    version "7.7.4"
    resolved "http://r.tnpm.oa.com/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.7.4.tgz#cb46348d2f8808e632f0ab048172130e636005f0"
    integrity sha1-y0Y0jS+ICOYy8KsEgXITDmNgBfA=
    dependencies:
      "@babel/types" "^7.7.4"
  
  "@babel/helper-plugin-utils@^7.0.0":
    version "7.0.0"
    resolved "http://r.tnpm.oa.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.0.0.tgz#bbb3fbee98661c569034237cc03967ba99b4f250"
    integrity sha1-u7P77phmHFaQNCN8wDlnupm08lA=
  
  "@babel/helper-split-export-declaration@^7.7.4":
    version "7.7.4"
    resolved "http://r.tnpm.oa.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.7.4.tgz#57292af60443c4a3622cf74040ddc28e68336fd8"
    integrity sha1-Vykq9gRDxKNiLPdAQN3Cjmgzb9g=
    dependencies:
      "@babel/types" "^7.7.4"
  
  "@babel/helpers@^7.7.4":
    version "7.7.4"
    resolved "http://r.tnpm.oa.com/@babel/helpers/download/@babel/helpers-7.7.4.tgz#62c215b9e6c712dadc15a9a0dcab76c92a940302"
    integrity sha1-YsIVuebHEtrcFamg3Kt2ySqUAwI=
    dependencies:
      "@babel/template" "^7.7.4"
      "@babel/traverse" "^7.7.4"
      "@babel/types" "^7.7.4"
  
  "@babel/highlight@^7.0.0":
    version "7.5.0"
    resolved "http://r.tnpm.oa.com/@babel/highlight/download/@babel/highlight-7.5.0.tgz#56d11312bd9248fa619591d02472be6e8cb32540"
    integrity sha1-VtETEr2SSPphlZHQJHK+boyzJUA=
    dependencies:
      chalk "^2.0.0"
      esutils "^2.0.2"
      js-tokens "^4.0.0"
  
  "@babel/parser@^7.1.0", "@babel/parser@^7.4.3", "@babel/parser@^7.7.4":
    version "7.7.4"
    resolved "http://r.tnpm.oa.com/@babel/parser/download/@babel/parser-7.7.4.tgz#75ab2d7110c2cf2fa949959afb05fa346d2231bb"
    integrity sha1-dastcRDCzy+pSZWa+wX6NG0iMbs=
  
  "@babel/plugin-syntax-object-rest-spread@^7.0.0":
    version "7.7.4"
    resolved "http://r.tnpm.oa.com/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.7.4.tgz#47cf220d19d6d0d7b154304701f468fc1cc6ff46"
    integrity sha1-R88iDRnW0NexVDBHAfRo/BzG/0Y=
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/template@^7.4.0", "@babel/template@^7.7.4":
    version "7.7.4"
    resolved "http://r.tnpm.oa.com/@babel/template/download/@babel/template-7.7.4.tgz#428a7d9eecffe27deac0a98e23bf8e3675d2a77b"
    integrity sha1-Qop9nuz/4n3qwKmOI7+ONnXSp3s=
    dependencies:
      "@babel/code-frame" "^7.0.0"
      "@babel/parser" "^7.7.4"
      "@babel/types" "^7.7.4"
  
  "@babel/traverse@^7.1.0", "@babel/traverse@^7.4.3", "@babel/traverse@^7.7.4":
    version "7.7.4"
    resolved "http://r.tnpm.oa.com/@babel/traverse/download/@babel/traverse-7.7.4.tgz#9c1e7c60fb679fe4fcfaa42500833333c2058558"
    integrity sha1-nB58YPtnn+T8+qQlAIMzM8IFhVg=
    dependencies:
      "@babel/code-frame" "^7.5.5"
      "@babel/generator" "^7.7.4"
      "@babel/helper-function-name" "^7.7.4"
      "@babel/helper-split-export-declaration" "^7.7.4"
      "@babel/parser" "^7.7.4"
      "@babel/types" "^7.7.4"
      debug "^4.1.0"
      globals "^11.1.0"
      lodash "^4.17.13"
  
  "@babel/types@^7.0.0", "@babel/types@^7.3.0", "@babel/types@^7.4.0", "@babel/types@^7.7.4":
    version "7.7.4"
    resolved "http://r.tnpm.oa.com/@babel/types/download/@babel/types-7.7.4.tgz#516570d539e44ddf308c07569c258ff94fde9193"
    integrity sha1-UWVw1TnkTd8wjAdWnCWP+U/ekZM=
    dependencies:
      esutils "^2.0.2"
      lodash "^4.17.13"
      to-fast-properties "^2.0.0"
  
  "@cnakazawa/watch@^1.0.3":
    version "1.0.3"
    resolved "http://r.tnpm.oa.com/@cnakazawa/watch/download/@cnakazawa/watch-1.0.3.tgz#099139eaec7ebf07a27c1786a3ff64f39464d2ef"
    integrity sha1-CZE56ux+vweifBeGo/9k85Rk0u8=
    dependencies:
      exec-sh "^0.3.2"
      minimist "^1.2.0"
  
  "@jest/console@^24.7.1", "@jest/console@^24.9.0":
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/@jest/console/download/@jest/console-24.9.0.tgz#79b1bc06fb74a8cfb01cbdedf945584b1b9707f0"
    integrity sha1-ebG8Bvt0qM+wHL3t+UVYSxuXB/A=
    dependencies:
      "@jest/source-map" "^24.9.0"
      chalk "^2.0.1"
      slash "^2.0.0"
  
  "@jest/core@^24.9.0":
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/@jest/core/download/@jest/core-24.9.0.tgz#2ceccd0b93181f9c4850e74f2a9ad43d351369c4"
    integrity sha1-LOzNC5MYH5xIUOdPKprUPTUTacQ=
    dependencies:
      "@jest/console" "^24.7.1"
      "@jest/reporters" "^24.9.0"
      "@jest/test-result" "^24.9.0"
      "@jest/transform" "^24.9.0"
      "@jest/types" "^24.9.0"
      ansi-escapes "^3.0.0"
      chalk "^2.0.1"
      exit "^0.1.2"
      graceful-fs "^4.1.15"
      jest-changed-files "^24.9.0"
      jest-config "^24.9.0"
      jest-haste-map "^24.9.0"
      jest-message-util "^24.9.0"
      jest-regex-util "^24.3.0"
      jest-resolve "^24.9.0"
      jest-resolve-dependencies "^24.9.0"
      jest-runner "^24.9.0"
      jest-runtime "^24.9.0"
      jest-snapshot "^24.9.0"
      jest-util "^24.9.0"
      jest-validate "^24.9.0"
      jest-watcher "^24.9.0"
      micromatch "^3.1.10"
      p-each-series "^1.0.0"
      realpath-native "^1.1.0"
      rimraf "^2.5.4"
      slash "^2.0.0"
      strip-ansi "^5.0.0"
  
  "@jest/environment@^24.9.0":
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/@jest/environment/download/@jest/environment-24.9.0.tgz#21e3afa2d65c0586cbd6cbefe208bafade44ab18"
    integrity sha1-IeOvotZcBYbL1svv4gi6+t5Eqxg=
    dependencies:
      "@jest/fake-timers" "^24.9.0"
      "@jest/transform" "^24.9.0"
      "@jest/types" "^24.9.0"
      jest-mock "^24.9.0"
  
  "@jest/fake-timers@^24.9.0":
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/@jest/fake-timers/download/@jest/fake-timers-24.9.0.tgz#ba3e6bf0eecd09a636049896434d306636540c93"
    integrity sha1-uj5r8O7NCaY2BJiWQ00wZjZUDJM=
    dependencies:
      "@jest/types" "^24.9.0"
      jest-message-util "^24.9.0"
      jest-mock "^24.9.0"
  
  "@jest/reporters@^24.9.0":
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/@jest/reporters/download/@jest/reporters-24.9.0.tgz#86660eff8e2b9661d042a8e98a028b8d631a5b43"
    integrity sha1-hmYO/44rlmHQQqjpigKLjWMaW0M=
    dependencies:
      "@jest/environment" "^24.9.0"
      "@jest/test-result" "^24.9.0"
      "@jest/transform" "^24.9.0"
      "@jest/types" "^24.9.0"
      chalk "^2.0.1"
      exit "^0.1.2"
      glob "^7.1.2"
      istanbul-lib-coverage "^2.0.2"
      istanbul-lib-instrument "^3.0.1"
      istanbul-lib-report "^2.0.4"
      istanbul-lib-source-maps "^3.0.1"
      istanbul-reports "^2.2.6"
      jest-haste-map "^24.9.0"
      jest-resolve "^24.9.0"
      jest-runtime "^24.9.0"
      jest-util "^24.9.0"
      jest-worker "^24.6.0"
      node-notifier "^5.4.2"
      slash "^2.0.0"
      source-map "^0.6.0"
      string-length "^2.0.0"
  
  "@jest/source-map@^24.3.0", "@jest/source-map@^24.9.0":
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/@jest/source-map/download/@jest/source-map-24.9.0.tgz#0e263a94430be4b41da683ccc1e6bffe2a191714"
    integrity sha1-DiY6lEML5LQdpoPMwea//ioZFxQ=
    dependencies:
      callsites "^3.0.0"
      graceful-fs "^4.1.15"
      source-map "^0.6.0"
  
  "@jest/test-result@^24.9.0":
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/@jest/test-result/download/@jest/test-result-24.9.0.tgz#11796e8aa9dbf88ea025757b3152595ad06ba0ca"
    integrity sha1-EXluiqnb+I6gJXV7MVJZWtBroMo=
    dependencies:
      "@jest/console" "^24.9.0"
      "@jest/types" "^24.9.0"
      "@types/istanbul-lib-coverage" "^2.0.0"
  
  "@jest/test-sequencer@^24.9.0":
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/@jest/test-sequencer/download/@jest/test-sequencer-24.9.0.tgz#f8f334f35b625a4f2f355f2fe7e6036dad2e6b31"
    integrity sha1-+PM081tiWk8vNV8v5+YDba0uazE=
    dependencies:
      "@jest/test-result" "^24.9.0"
      jest-haste-map "^24.9.0"
      jest-runner "^24.9.0"
      jest-runtime "^24.9.0"
  
  "@jest/transform@^24.9.0":
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/@jest/transform/download/@jest/transform-24.9.0.tgz#4ae2768b296553fadab09e9ec119543c90b16c56"
    integrity sha1-SuJ2iyllU/rasJ6ewRlUPJCxbFY=
    dependencies:
      "@babel/core" "^7.1.0"
      "@jest/types" "^24.9.0"
      babel-plugin-istanbul "^5.1.0"
      chalk "^2.0.1"
      convert-source-map "^1.4.0"
      fast-json-stable-stringify "^2.0.0"
      graceful-fs "^4.1.15"
      jest-haste-map "^24.9.0"
      jest-regex-util "^24.9.0"
      jest-util "^24.9.0"
      micromatch "^3.1.10"
      pirates "^4.0.1"
      realpath-native "^1.1.0"
      slash "^2.0.0"
      source-map "^0.6.1"
      write-file-atomic "2.4.1"
  
  "@jest/types@^24.9.0":
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/@jest/types/download/@jest/types-24.9.0.tgz#63cb26cb7500d069e5a389441a7c6ab5e909fc59"
    integrity sha1-Y8smy3UA0Gnlo4lEGnxqtekJ/Fk=
    dependencies:
      "@types/istanbul-lib-coverage" "^2.0.0"
      "@types/istanbul-reports" "^1.1.1"
      "@types/yargs" "^13.0.0"
  
  "@nodelib/fs.scandir@2.1.3":
    version "2.1.3"
    resolved "http://r.tnpm.oa.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.3.tgz#3a582bdb53804c6ba6d146579c46e52130cf4a3b"
    integrity sha1-Olgr21OATGum0UZXnEblITDPSjs=
    dependencies:
      "@nodelib/fs.stat" "2.0.3"
      run-parallel "^1.1.9"
  
  "@nodelib/fs.stat@2.0.3", "@nodelib/fs.stat@^2.0.2":
    version "2.0.3"
    resolved "http://r.tnpm.oa.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.3.tgz#34dc5f4cabbc720f4e60f75a747e7ecd6c175bd3"
    integrity sha1-NNxfTKu8cg9OYPdadH5+zWwXW9M=
  
  "@nodelib/fs.walk@^1.2.3":
    version "1.2.4"
    resolved "http://r.tnpm.oa.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.4.tgz#011b9202a70a6366e436ca5c065844528ab04976"
    integrity sha1-ARuSAqcKY2bkNspcBlhEUoqwSXY=
    dependencies:
      "@nodelib/fs.scandir" "2.1.3"
      fastq "^1.6.0"
  
  "@samverschueren/stream-to-observable@^0.3.0":
    version "0.3.0"
    resolved "http://r.tnpm.oa.com/@samverschueren/stream-to-observable/download/@samverschueren/stream-to-observable-0.3.0.tgz#ecdf48d532c58ea477acfcab80348424f8d0662f"
    integrity sha1-7N9I1TLFjqR3rPyrgDSEJPjQZi8=
    dependencies:
      any-observable "^0.3.0"
  
  "@types/babel__core@^7.1.0":
    version "7.1.3"
    resolved "http://r.tnpm.oa.com/@types/babel__core/download/@types/babel__core-7.1.3.tgz#e441ea7df63cd080dfcd02ab199e6d16a735fc30"
    integrity sha1-5EHqffY80IDfzQKrGZ5tFqc1/DA=
    dependencies:
      "@babel/parser" "^7.1.0"
      "@babel/types" "^7.0.0"
      "@types/babel__generator" "*"
      "@types/babel__template" "*"
      "@types/babel__traverse" "*"
  
  "@types/babel__generator@*":
    version "7.6.0"
    resolved "http://r.tnpm.oa.com/@types/babel__generator/download/@types/babel__generator-7.6.0.tgz#f1ec1c104d1bb463556ecb724018ab788d0c172a"
    integrity sha1-8ewcEE0btGNVbstyQBireI0MFyo=
    dependencies:
      "@babel/types" "^7.0.0"
  
  "@types/babel__template@*":
    version "7.0.2"
    resolved "http://r.tnpm.oa.com/@types/babel__template/download/@types/babel__template-7.0.2.tgz#4ff63d6b52eddac1de7b975a5223ed32ecea9307"
    integrity sha1-T/Y9a1Lt2sHee5daUiPtMuzqkwc=
    dependencies:
      "@babel/parser" "^7.1.0"
      "@babel/types" "^7.0.0"
  
  "@types/babel__traverse@*", "@types/babel__traverse@^7.0.6":
    version "7.0.8"
    resolved "http://r.tnpm.oa.com/@types/babel__traverse/download/@types/babel__traverse-7.0.8.tgz#479a4ee3e291a403a1096106013ec22cf9b64012"
    integrity sha1-R5pO4+KRpAOhCWEGAT7CLPm2QBI=
    dependencies:
      "@babel/types" "^7.3.0"
  
  "@types/clone@^0.1.30":
    version "0.1.30"
    resolved "http://r.tnpm.oa.com/@types/clone/download/@types/clone-0.1.30.tgz#e7365648c1b42136a59c7d5040637b3b5c83b614"
    integrity sha1-5zZWSMG0ITalnH1QQGN7O1yDthQ=
  
  "@types/eslint-visitor-keys@^1.0.0":
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/@types/eslint-visitor-keys/download/@types/eslint-visitor-keys-1.0.0.tgz#1ee30d79544ca84d68d4b3cdb0af4f205663dd2d"
    integrity sha1-HuMNeVRMqE1o1LPNsK9PIFZj3S0=
  
  "@types/events@*":
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/@types/events/download/@types/events-3.0.0.tgz#2862f3f58a9a7f7c3e78d79f130dd4d71c25c2a7"
    integrity sha1-KGLz9Yqaf3w+eNefEw3U1xwlwqc=
  
  "@types/glob@^7.1.1":
    version "7.1.1"
    resolved "http://r.tnpm.oa.com/@types/glob/download/@types/glob-7.1.1.tgz#aa59a1c6e3fbc421e07ccd31a944c30eba521575"
    integrity sha1-qlmhxuP7xCHgfM0xqUTDDrpSFXU=
    dependencies:
      "@types/events" "*"
      "@types/minimatch" "*"
      "@types/node" "*"
  
  "@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0":
    version "2.0.1"
    resolved "http://r.tnpm.oa.com/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.1.tgz#42995b446db9a48a11a07ec083499a860e9138ff"
    integrity sha1-QplbRG25pIoRoH7Ag0mahg6ROP8=
  
  "@types/istanbul-lib-report@*":
    version "1.1.1"
    resolved "http://r.tnpm.oa.com/@types/istanbul-lib-report/download/@types/istanbul-lib-report-1.1.1.tgz#e5471e7fa33c61358dd38426189c037a58433b8c"
    integrity sha1-5Ucef6M8YTWN04QmGJwDelhDO4w=
    dependencies:
      "@types/istanbul-lib-coverage" "*"
  
  "@types/istanbul-reports@^1.1.1":
    version "1.1.1"
    resolved "http://r.tnpm.oa.com/@types/istanbul-reports/download/@types/istanbul-reports-1.1.1.tgz#7a8cbf6a406f36c8add871625b278eaf0b0d255a"
    integrity sha1-eoy/akBvNsit2HFiWyeOrwsNJVo=
    dependencies:
      "@types/istanbul-lib-coverage" "*"
      "@types/istanbul-lib-report" "*"
  
  "@types/jest@^24.0.23":
    version "24.0.23"
    resolved "http://r.tnpm.oa.com/@types/jest/download/@types/jest-24.0.23.tgz#046f8e2ade026fe831623e361a36b6fb9a4463e4"
    integrity sha1-BG+OKt4Cb+gxYj42Gja2+5pEY+Q=
    dependencies:
      jest-diff "^24.3.0"
  
  "@types/json-schema@^7.0.3":
    version "7.0.3"
    resolved "http://r.tnpm.oa.com/@types/json-schema/download/@types/json-schema-7.0.3.tgz#bdfd69d61e464dcc81b25159c270d75a73c1a636"
    integrity sha1-vf1p1h5GTcyBslFZwnDXWnPBpjY=
  
  "@types/minimatch@*":
    version "3.0.3"
    resolved "http://r.tnpm.oa.com/@types/minimatch/download/@types/minimatch-3.0.3.tgz#3dca0e3f33b200fc7d1139c0cd96c1268cadfd9d"
    integrity sha1-PcoOPzOyAPx9ETnAzZbBJoyt/Z0=
  
  "@types/node@*":
    version "12.12.14"
    resolved "http://r.tnpm.oa.com/@types/node/download/@types/node-12.12.14.tgz#1c1d6e3c75dba466e0326948d56e8bd72a1903d2"
    integrity sha1-HB1uPHXbpGbgMmlI1W6L1yoZA9I=
  
  "@types/node@10.12.10":
    version "10.12.10"
    resolved "http://r.tnpm.oa.com/@types/node/download/@types/node-10.12.10.tgz#4fa76e6598b7de3f0cb6ec3abacc4f59e5b3a2ce"
    integrity sha1-T6duZZi33j8Mtuw6usxPWeWzos4=
  
  "@types/normalize-package-data@^2.4.0":
    version "2.4.0"
    resolved "http://r.tnpm.oa.com/@types/normalize-package-data/download/@types/normalize-package-data-2.4.0.tgz#e486d0d97396d79beedd0a6e33f4534ff6b4973e"
    integrity sha1-5IbQ2XOW15vu3QpuM/RTT/a0lz4=
  
  "@types/stack-utils@^1.0.1":
    version "1.0.1"
    resolved "http://r.tnpm.oa.com/@types/stack-utils/download/@types/stack-utils-1.0.1.tgz#0a851d3bd96498fa25c33ab7278ed3bd65f06c3e"
    integrity sha1-CoUdO9lkmPolwzq3J47TvWXwbD4=
  
  "@types/yargs-parser@*":
    version "13.1.0"
    resolved "http://r.tnpm.oa.com/@types/yargs-parser/download/@types/yargs-parser-13.1.0.tgz#c563aa192f39350a1d18da36c5a8da382bbd8228"
    integrity sha1-xWOqGS85NQodGNo2xajaOCu9gig=
  
  "@types/yargs@^13.0.0":
    version "13.0.3"
    resolved "http://r.tnpm.oa.com/@types/yargs/download/@types/yargs-13.0.3.tgz#76482af3981d4412d65371a318f992d33464a380"
    integrity sha1-dkgq85gdRBLWU3GjGPmS0zRko4A=
    dependencies:
      "@types/yargs-parser" "*"
  
  "@typescript-eslint/eslint-plugin@^2.10.0":
    version "2.10.0"
    resolved "http://r.tnpm.oa.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-2.10.0.tgz#c4cb103275e555e8a7e9b3d14c5951eb6d431e70"
    integrity sha1-xMsQMnXlVein6bPRTFlR621DHnA=
    dependencies:
      "@typescript-eslint/experimental-utils" "2.10.0"
      eslint-utils "^1.4.3"
      functional-red-black-tree "^1.0.1"
      regexpp "^3.0.0"
      tsutils "^3.17.1"
  
  "@typescript-eslint/experimental-utils@2.10.0":
    version "2.10.0"
    resolved "http://r.tnpm.oa.com/@typescript-eslint/experimental-utils/download/@typescript-eslint/experimental-utils-2.10.0.tgz#8db1656cdfd3d9dcbdbf360b8274dea76f0b2c2c"
    integrity sha1-jbFlbN/T2dy9vzYLgnTep28LLCw=
    dependencies:
      "@types/json-schema" "^7.0.3"
      "@typescript-eslint/typescript-estree" "2.10.0"
      eslint-scope "^5.0.0"
  
  "@typescript-eslint/parser@^2.10.0":
    version "2.10.0"
    resolved "http://r.tnpm.oa.com/@typescript-eslint/parser/download/@typescript-eslint/parser-2.10.0.tgz#24b2e48384ab6d5a6121e4c4faf8892c79657ad3"
    integrity sha1-JLLkg4SrbVphIeTE+viJLHlletM=
    dependencies:
      "@types/eslint-visitor-keys" "^1.0.0"
      "@typescript-eslint/experimental-utils" "2.10.0"
      "@typescript-eslint/typescript-estree" "2.10.0"
      eslint-visitor-keys "^1.1.0"
  
  "@typescript-eslint/typescript-estree@2.10.0":
    version "2.10.0"
    resolved "http://r.tnpm.oa.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-2.10.0.tgz#89cdabd5e8c774e9d590588cb42fb9afd14dcbd9"
    integrity sha1-ic2r1ejHdOnVkFiMtC+5r9FNy9k=
    dependencies:
      debug "^4.1.1"
      eslint-visitor-keys "^1.1.0"
      glob "^7.1.6"
      is-glob "^4.0.1"
      lodash.unescape "4.0.1"
      semver "^6.3.0"
      tsutils "^3.17.1"
  
  abab@^2.0.0:
    version "2.0.3"
    resolved "http://r.tnpm.oa.com/abab/download/abab-2.0.3.tgz#623e2075e02eb2d3f2475e49f99c91846467907a"
    integrity sha1-Yj4gdeAustPyR15J+ZyRhGRnkHo=
  
  abbrev@1:
    version "1.1.1"
    resolved "http://r.tnpm.oa.com/abbrev/download/abbrev-1.1.1.tgz#f8f2c887ad10bf67f634f005b6987fed3179aac8"
    integrity sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=
  
  acorn-globals@^4.1.0:
    version "4.3.4"
    resolved "http://r.tnpm.oa.com/acorn-globals/download/acorn-globals-4.3.4.tgz#9fa1926addc11c97308c4e66d7add0d40c3272e7"
    integrity sha1-n6GSat3BHJcwjE5m163Q1Awycuc=
    dependencies:
      acorn "^6.0.1"
      acorn-walk "^6.0.1"
  
  acorn-jsx@^5.1.0:
    version "5.1.0"
    resolved "http://r.tnpm.oa.com/acorn-jsx/download/acorn-jsx-5.1.0.tgz#294adb71b57398b0680015f0a38c563ee1db5384"
    integrity sha1-KUrbcbVzmLBoABXwo4xWPuHbU4Q=
  
  acorn-walk@^6.0.1:
    version "6.2.0"
    resolved "http://r.tnpm.oa.com/acorn-walk/download/acorn-walk-6.2.0.tgz#123cb8f3b84c2171f1f7fb252615b1c78a6b1a8c"
    integrity sha1-Ejy487hMIXHx9/slJhWxx4prGow=
  
  acorn@^5.5.3:
    version "5.7.3"
    resolved "http://r.tnpm.oa.com/acorn/download/acorn-5.7.3.tgz#67aa231bf8812974b85235a96771eb6bd07ea279"
    integrity sha1-Z6ojG/iBKXS4UjWpZ3Hra9B+onk=
  
  acorn@^6.0.1:
    version "6.4.0"
    resolved "http://r.tnpm.oa.com/acorn/download/acorn-6.4.0.tgz#b659d2ffbafa24baf5db1cdbb2c94a983ecd2784"
    integrity sha1-tlnS/7r6JLr12xzbsslKmD7NJ4Q=
  
  acorn@^7.1.0:
    version "7.1.0"
    resolved "http://r.tnpm.oa.com/acorn/download/acorn-7.1.0.tgz#949d36f2c292535da602283586c2477c57eb2d6c"
    integrity sha1-lJ028sKSU12mAig1hsJHfFfrLWw=
  
  aggregate-error@^3.0.0:
    version "3.0.1"
    resolved "http://r.tnpm.oa.com/aggregate-error/download/aggregate-error-3.0.1.tgz#db2fe7246e536f40d9b5442a39e117d7dd6a24e0"
    integrity sha1-2y/nJG5Tb0DZtUQqOeEX191qJOA=
    dependencies:
      clean-stack "^2.0.0"
      indent-string "^4.0.0"
  
  ajv@^6.10.0, ajv@^6.10.2, ajv@^6.5.5:
    version "6.10.2"
    resolved "http://r.tnpm.oa.com/ajv/download/ajv-6.10.2.tgz#d3cea04d6b017b2894ad69040fec8b623eb4bd52"
    integrity sha1-086gTWsBeyiUrWkED+yLYj60vVI=
    dependencies:
      fast-deep-equal "^2.0.1"
      fast-json-stable-stringify "^2.0.0"
      json-schema-traverse "^0.4.1"
      uri-js "^4.2.2"
  
  ansi-escapes@^3.0.0:
    version "3.2.0"
    resolved "http://r.tnpm.oa.com/ansi-escapes/download/ansi-escapes-3.2.0.tgz#8780b98ff9dbf5638152d1f1fe5c1d7b4442976b"
    integrity sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=
  
  ansi-escapes@^4.2.1:
    version "4.3.0"
    resolved "http://r.tnpm.oa.com/ansi-escapes/download/ansi-escapes-4.3.0.tgz#a4ce2b33d6b214b7950d8595c212f12ac9cc569d"
    integrity sha1-pM4rM9ayFLeVDYWVwhLxKsnMVp0=
    dependencies:
      type-fest "^0.8.1"
  
  ansi-regex@^2.0.0:
    version "2.1.1"
    resolved "http://r.tnpm.oa.com/ansi-regex/download/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"
    integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=
  
  ansi-regex@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/ansi-regex/download/ansi-regex-3.0.0.tgz#ed0317c322064f79466c02966bddb605ab37d998"
    integrity sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=
  
  ansi-regex@^4.0.0, ansi-regex@^4.1.0:
    version "4.1.0"
    resolved "http://r.tnpm.oa.com/ansi-regex/download/ansi-regex-4.1.0.tgz#8b9f8f08cf1acb843756a839ca8c7e3168c51997"
    integrity sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc=
  
  ansi-regex@^5.0.0:
    version "5.0.0"
    resolved "http://r.tnpm.oa.com/ansi-regex/download/ansi-regex-5.0.0.tgz#388539f55179bf39339c81af30a654d69f87cb75"
    integrity sha1-OIU59VF5vzkznIGvMKZU1p+Hy3U=
  
  ansi-styles@^2.2.1:
    version "2.2.1"
    resolved "http://r.tnpm.oa.com/ansi-styles/download/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"
    integrity sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=
  
  ansi-styles@^3.2.0, ansi-styles@^3.2.1:
    version "3.2.1"
    resolved "http://r.tnpm.oa.com/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
    integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
    dependencies:
      color-convert "^1.9.0"
  
  any-observable@^0.3.0:
    version "0.3.0"
    resolved "http://r.tnpm.oa.com/any-observable/download/any-observable-0.3.0.tgz#af933475e5806a67d0d7df090dd5e8bef65d119b"
    integrity sha1-r5M0deWAamfQ198JDdXovvZdEZs=
  
  anymatch@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/anymatch/download/anymatch-2.0.0.tgz#bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb"
    integrity sha1-vLJLTzeTTZqnrBe0ra+J58du8us=
    dependencies:
      micromatch "^3.1.4"
      normalize-path "^2.1.1"
  
  aproba@^1.0.3:
    version "1.2.0"
    resolved "http://r.tnpm.oa.com/aproba/download/aproba-1.2.0.tgz#6802e6264efd18c790a1b0d517f0f2627bf2c94a"
    integrity sha1-aALmJk79GMeQobDVF/DyYnvyyUo=
  
  are-we-there-yet@~1.1.2:
    version "1.1.5"
    resolved "http://r.tnpm.oa.com/are-we-there-yet/download/are-we-there-yet-1.1.5.tgz#4b35c2944f062a8bfcda66410760350fe9ddfc21"
    integrity sha1-SzXClE8GKov82mZBB2A1D+nd/CE=
    dependencies:
      delegates "^1.0.0"
      readable-stream "^2.0.6"
  
  argparse@^1.0.7:
    version "1.0.10"
    resolved "http://r.tnpm.oa.com/argparse/download/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
    integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
    dependencies:
      sprintf-js "~1.0.2"
  
  arr-diff@^4.0.0:
    version "4.0.0"
    resolved "http://r.tnpm.oa.com/arr-diff/download/arr-diff-4.0.0.tgz#d6461074febfec71e7e15235761a329a5dc7c520"
    integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=
  
  arr-flatten@^1.1.0:
    version "1.1.0"
    resolved "http://r.tnpm.oa.com/arr-flatten/download/arr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1"
    integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=
  
  arr-union@^3.1.0:
    version "3.1.0"
    resolved "http://r.tnpm.oa.com/arr-union/download/arr-union-3.1.0.tgz#e39b09aea9def866a8f206e288af63919bae39c4"
    integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=
  
  array-equal@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/array-equal/download/array-equal-1.0.0.tgz#8c2a5ef2472fd9ea742b04c77a75093ba2757c93"
    integrity sha1-jCpe8kcv2ep0KwTHenUJO6J1fJM=
  
  array-union@^2.1.0:
    version "2.1.0"
    resolved "http://r.tnpm.oa.com/array-union/download/array-union-2.1.0.tgz#b798420adbeb1de828d84acd8a2e23d3efe85e8d"
    integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=
  
  array-unique@^0.3.2:
    version "0.3.2"
    resolved "http://r.tnpm.oa.com/array-unique/download/array-unique-0.3.2.tgz#a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428"
    integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=
  
  asn1@~0.2.3:
    version "0.2.4"
    resolved "http://r.tnpm.oa.com/asn1/download/asn1-0.2.4.tgz#8d2475dfab553bb33e77b54e59e880bb8ce23136"
    integrity sha1-jSR136tVO7M+d7VOWeiAu4ziMTY=
    dependencies:
      safer-buffer "~2.1.0"
  
  assert-plus@1.0.0, assert-plus@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/assert-plus/download/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"
    integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=
  
  assign-symbols@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/assign-symbols/download/assign-symbols-1.0.0.tgz#59667f41fadd4f20ccbc2bb96b8d4f7f78ec0367"
    integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=
  
  astral-regex@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/astral-regex/download/astral-regex-1.0.0.tgz#6c8c3fb827dd43ee3918f27b82782ab7658a6fd9"
    integrity sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=
  
  async-limiter@~1.0.0:
    version "1.0.1"
    resolved "http://r.tnpm.oa.com/async-limiter/download/async-limiter-1.0.1.tgz#dd379e94f0db8310b08291f9d64c3209766617fd"
    integrity sha1-3TeelPDbgxCwgpH51kwyCXZmF/0=
  
  asynckit@^0.4.0:
    version "0.4.0"
    resolved "http://r.tnpm.oa.com/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
    integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=
  
  atob@^2.1.1:
    version "2.1.2"
    resolved "http://r.tnpm.oa.com/atob/download/atob-2.1.2.tgz#6d9517eb9e030d2436666651e86bd9f6f13533c9"
    integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=
  
  aws-sign2@~0.7.0:
    version "0.7.0"
    resolved "http://r.tnpm.oa.com/aws-sign2/download/aws-sign2-0.7.0.tgz#b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8"
    integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=
  
  aws4@^1.8.0:
    version "1.9.0"
    resolved "http://r.tnpm.oa.com/aws4/download/aws4-1.9.0.tgz#24390e6ad61386b0a747265754d2a17219de862c"
    integrity sha1-JDkOatYThrCnRyZXVNKhchnehiw=
  
  babel-jest@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/babel-jest/download/babel-jest-24.9.0.tgz#3fc327cb8467b89d14d7bc70e315104a783ccd54"
    integrity sha1-P8Mny4RnuJ0U17xw4xUQSng8zVQ=
    dependencies:
      "@jest/transform" "^24.9.0"
      "@jest/types" "^24.9.0"
      "@types/babel__core" "^7.1.0"
      babel-plugin-istanbul "^5.1.0"
      babel-preset-jest "^24.9.0"
      chalk "^2.4.2"
      slash "^2.0.0"
  
  babel-plugin-istanbul@^5.1.0:
    version "5.2.0"
    resolved "http://r.tnpm.oa.com/babel-plugin-istanbul/download/babel-plugin-istanbul-5.2.0.tgz#df4ade83d897a92df069c4d9a25cf2671293c854"
    integrity sha1-30reg9iXqS3wacTZolzyZxKTyFQ=
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
      find-up "^3.0.0"
      istanbul-lib-instrument "^3.3.0"
      test-exclude "^5.2.3"
  
  babel-plugin-jest-hoist@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-24.9.0.tgz#4f837091eb407e01447c8843cbec546d0002d756"
    integrity sha1-T4NwketAfgFEfIhDy+xUbQAC11Y=
    dependencies:
      "@types/babel__traverse" "^7.0.6"
  
  babel-preset-jest@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/babel-preset-jest/download/babel-preset-jest-24.9.0.tgz#192b521e2217fb1d1f67cf73f70c336650ad3cdc"
    integrity sha1-GStSHiIX+x0fZ89z9wwzZlCtPNw=
    dependencies:
      "@babel/plugin-syntax-object-rest-spread" "^7.0.0"
      babel-plugin-jest-hoist "^24.9.0"
  
  balanced-match@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/balanced-match/download/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"
    integrity sha1-ibTRmasr7kneFk6gK4nORi1xt2c=
  
  base@^0.11.1:
    version "0.11.2"
    resolved "http://r.tnpm.oa.com/base/download/base-0.11.2.tgz#7bde5ced145b6d551a90db87f83c558b4eb48a8f"
    integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
    dependencies:
      cache-base "^1.0.1"
      class-utils "^0.3.5"
      component-emitter "^1.2.1"
      define-property "^1.0.0"
      isobject "^3.0.1"
      mixin-deep "^1.2.0"
      pascalcase "^0.1.1"
  
  bcrypt-pbkdf@^1.0.0:
    version "1.0.2"
    resolved "http://r.tnpm.oa.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz#a4301d389b6a43f9b67ff3ca11a3f6637e360e9e"
    integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
    dependencies:
      tweetnacl "^0.14.3"
  
  brace-expansion@^1.1.7:
    version "1.1.11"
    resolved "http://r.tnpm.oa.com/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
    integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
    dependencies:
      balanced-match "^1.0.0"
      concat-map "0.0.1"
  
  braces@^2.3.1:
    version "2.3.2"
    resolved "http://r.tnpm.oa.com/braces/download/braces-2.3.2.tgz#5979fd3f14cd531565e5fa2df1abfff1dfaee729"
    integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
    dependencies:
      arr-flatten "^1.1.0"
      array-unique "^0.3.2"
      extend-shallow "^2.0.1"
      fill-range "^4.0.0"
      isobject "^3.0.1"
      repeat-element "^1.1.2"
      snapdragon "^0.8.1"
      snapdragon-node "^2.0.1"
      split-string "^3.0.2"
      to-regex "^3.0.1"
  
  braces@^3.0.1:
    version "3.0.2"
    resolved "http://r.tnpm.oa.com/braces/download/braces-3.0.2.tgz#3454e1a462ee8d599e236df336cd9ea4f8afe107"
    integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
    dependencies:
      fill-range "^7.0.1"
  
  browser-process-hrtime@^0.1.2:
    version "0.1.3"
    resolved "http://r.tnpm.oa.com/browser-process-hrtime/download/browser-process-hrtime-0.1.3.tgz#616f00faef1df7ec1b5bf9cfe2bdc3170f26c7b4"
    integrity sha1-YW8A+u8d9+wbW/nP4r3DFw8mx7Q=
  
  browser-resolve@^1.11.3:
    version "1.11.3"
    resolved "http://r.tnpm.oa.com/browser-resolve/download/browser-resolve-1.11.3.tgz#9b7cbb3d0f510e4cb86bdbd796124d28b5890af6"
    integrity sha1-m3y7PQ9RDky4a9vXlhJNKLWJCvY=
    dependencies:
      resolve "1.1.7"
  
  bs-logger@0.x:
    version "0.2.6"
    resolved "http://r.tnpm.oa.com/bs-logger/download/bs-logger-0.2.6.tgz#eb7d365307a72cf974cc6cda76b68354ad336bd8"
    integrity sha1-6302UwenLPl0zGzadraDVK0za9g=
    dependencies:
      fast-json-stable-stringify "2.x"
  
  bser@^2.0.0:
    version "2.1.1"
    resolved "http://r.tnpm.oa.com/bser/download/bser-2.1.1.tgz#e6787da20ece9d07998533cfd9de6f5c38f4bc05"
    integrity sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=
    dependencies:
      node-int64 "^0.4.0"
  
  buffer-from@1.x, buffer-from@^1.0.0:
    version "1.1.1"
    resolved "http://r.tnpm.oa.com/buffer-from/download/buffer-from-1.1.1.tgz#32713bc028f75c02fdb710d7c7bcec1f2c6070ef"
    integrity sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8=
  
  cache-base@^1.0.1:
    version "1.0.1"
    resolved "http://r.tnpm.oa.com/cache-base/download/cache-base-1.0.1.tgz#0a7f46416831c8b662ee36fe4e7c59d76f666ab2"
    integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
    dependencies:
      collection-visit "^1.0.0"
      component-emitter "^1.2.1"
      get-value "^2.0.6"
      has-value "^1.0.0"
      isobject "^3.0.1"
      set-value "^2.0.0"
      to-object-path "^0.3.0"
      union-value "^1.0.0"
      unset-value "^1.0.0"
  
  caller-callsite@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/caller-callsite/download/caller-callsite-2.0.0.tgz#847e0fce0a223750a9a027c54b33731ad3154134"
    integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
    dependencies:
      callsites "^2.0.0"
  
  caller-path@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/caller-path/download/caller-path-2.0.0.tgz#468f83044e369ab2010fac5f06ceee15bb2cb1f4"
    integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
    dependencies:
      caller-callsite "^2.0.0"
  
  callsites@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/callsites/download/callsites-2.0.0.tgz#06eb84f00eea413da86affefacbffb36093b3c50"
    integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=
  
  callsites@^3.0.0:
    version "3.1.0"
    resolved "http://r.tnpm.oa.com/callsites/download/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
    integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=
  
  camelcase@^4.1.0:
    version "4.1.0"
    resolved "http://r.tnpm.oa.com/camelcase/download/camelcase-4.1.0.tgz#d545635be1e33c542649c69173e5de6acfae34dd"
    integrity sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=
  
  camelcase@^5.0.0, camelcase@^5.3.1:
    version "5.3.1"
    resolved "http://r.tnpm.oa.com/camelcase/download/camelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"
    integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=
  
  capture-exit@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/capture-exit/download/capture-exit-2.0.0.tgz#fb953bfaebeb781f62898239dabb426d08a509a4"
    integrity sha1-+5U7+uvreB9iiYI52rtCbQilCaQ=
    dependencies:
      rsvp "^4.8.4"
  
  caseless@~0.12.0:
    version "0.12.0"
    resolved "http://r.tnpm.oa.com/caseless/download/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"
    integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=
  
  chalk@^1.0.0, chalk@^1.1.3:
    version "1.1.3"
    resolved "http://r.tnpm.oa.com/chalk/download/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
    integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
    dependencies:
      ansi-styles "^2.2.1"
      escape-string-regexp "^1.0.2"
      has-ansi "^2.0.0"
      strip-ansi "^3.0.0"
      supports-color "^2.0.0"
  
  chalk@^2.0.0, chalk@^2.0.1, chalk@^2.1.0, chalk@^2.4.1, chalk@^2.4.2:
    version "2.4.2"
    resolved "http://r.tnpm.oa.com/chalk/download/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
    integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
    dependencies:
      ansi-styles "^3.2.1"
      escape-string-regexp "^1.0.5"
      supports-color "^5.3.0"
  
  chardet@^0.7.0:
    version "0.7.0"
    resolved "http://r.tnpm.oa.com/chardet/download/chardet-0.7.0.tgz#90094849f0937f2eedc2425d0d28a9e5f0cbad9e"
    integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=
  
  chownr@^1.1.1:
    version "1.1.3"
    resolved "http://r.tnpm.oa.com/chownr/download/chownr-1.1.3.tgz#42d837d5239688d55f303003a508230fa6727142"
    integrity sha1-Qtg31SOWiNVfMDADpQgjD6ZycUI=
  
  ci-info@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/ci-info/download/ci-info-2.0.0.tgz#67a9e964be31a51e15e5010d58e6f12834002f46"
    integrity sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y=
  
  class-utils@^0.3.5:
    version "0.3.6"
    resolved "http://r.tnpm.oa.com/class-utils/download/class-utils-0.3.6.tgz#f93369ae8b9a7ce02fd41faad0ca83033190c463"
    integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
    dependencies:
      arr-union "^3.1.0"
      define-property "^0.2.5"
      isobject "^3.0.0"
      static-extend "^0.1.1"
  
  clean-stack@^2.0.0:
    version "2.2.0"
    resolved "http://r.tnpm.oa.com/clean-stack/download/clean-stack-2.2.0.tgz#ee8472dbb129e727b31e8a10a427dee9dfe4008b"
    integrity sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=
  
  cli-cursor@^2.0.0, cli-cursor@^2.1.0:
    version "2.1.0"
    resolved "http://r.tnpm.oa.com/cli-cursor/download/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
    integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
    dependencies:
      restore-cursor "^2.0.0"
  
  cli-cursor@^3.1.0:
    version "3.1.0"
    resolved "http://r.tnpm.oa.com/cli-cursor/download/cli-cursor-3.1.0.tgz#264305a7ae490d1d03bf0c9ba7c925d1753af307"
    integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
    dependencies:
      restore-cursor "^3.1.0"
  
  cli-truncate@^0.2.1:
    version "0.2.1"
    resolved "http://r.tnpm.oa.com/cli-truncate/download/cli-truncate-0.2.1.tgz#9f15cfbb0705005369216c626ac7d05ab90dd574"
    integrity sha1-nxXPuwcFAFNpIWxiasfQWrkN1XQ=
    dependencies:
      slice-ansi "0.0.4"
      string-width "^1.0.1"
  
  cli-width@^2.0.0:
    version "2.2.0"
    resolved "http://r.tnpm.oa.com/cli-width/download/cli-width-2.2.0.tgz#ff19ede8a9a5e579324147b0c11f0fbcbabed639"
    integrity sha1-/xnt6Kml5XkyQUewwR8PvLq+1jk=
  
  cliui@^5.0.0:
    version "5.0.0"
    resolved "http://r.tnpm.oa.com/cliui/download/cliui-5.0.0.tgz#deefcfdb2e800784aa34f46fa08e06851c7bbbc5"
    integrity sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=
    dependencies:
      string-width "^3.1.0"
      strip-ansi "^5.2.0"
      wrap-ansi "^5.1.0"
  
  clone@^2.1.2:
    version "2.1.2"
    resolved "http://r.tnpm.oa.com/clone/download/clone-2.1.2.tgz#1b7f4b9f591f1e8f83670401600345a02887435f"
    integrity sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=
  
  co@^4.6.0:
    version "4.6.0"
    resolved "http://r.tnpm.oa.com/co/download/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"
    integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=
  
  code-point-at@^1.0.0:
    version "1.1.0"
    resolved "http://r.tnpm.oa.com/code-point-at/download/code-point-at-1.1.0.tgz#0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77"
    integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=
  
  collection-visit@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/collection-visit/download/collection-visit-1.0.0.tgz#4bc0373c164bc3291b4d368c829cf1a80a59dca0"
    integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
    dependencies:
      map-visit "^1.0.0"
      object-visit "^1.0.0"
  
  color-convert@^1.9.0:
    version "1.9.3"
    resolved "http://r.tnpm.oa.com/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
    integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
    dependencies:
      color-name "1.1.3"
  
  color-name@1.1.3:
    version "1.1.3"
    resolved "http://r.tnpm.oa.com/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
    integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=
  
  combined-stream@^1.0.6, combined-stream@~1.0.6:
    version "1.0.8"
    resolved "http://r.tnpm.oa.com/combined-stream/download/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
    integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
    dependencies:
      delayed-stream "~1.0.0"
  
  commander@^2.20.0, commander@~2.20.3:
    version "2.20.3"
    resolved "http://r.tnpm.oa.com/commander/download/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
    integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=
  
  component-emitter@^1.2.1:
    version "1.3.0"
    resolved "http://r.tnpm.oa.com/component-emitter/download/component-emitter-1.3.0.tgz#16e4070fba8ae29b679f2215853ee181ab2eabc0"
    integrity sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=
  
  concat-map@0.0.1:
    version "0.0.1"
    resolved "http://r.tnpm.oa.com/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
    integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=
  
  console-control-strings@^1.0.0, console-control-strings@~1.1.0:
    version "1.1.0"
    resolved "http://r.tnpm.oa.com/console-control-strings/download/console-control-strings-1.1.0.tgz#3d7cf4464db6446ea644bf4b39507f9851008e8e"
    integrity sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=
  
  convert-source-map@^1.4.0, convert-source-map@^1.7.0:
    version "1.7.0"
    resolved "http://r.tnpm.oa.com/convert-source-map/download/convert-source-map-1.7.0.tgz#17a2cb882d7f77d3490585e2ce6c524424a3a442"
    integrity sha1-F6LLiC1/d9NJBYXizmxSRCSjpEI=
    dependencies:
      safe-buffer "~5.1.1"
  
  copy-descriptor@^0.1.0:
    version "0.1.1"
    resolved "http://r.tnpm.oa.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz#676f6eb3c39997c2ee1ac3a924fd6124748f578d"
    integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=
  
  core-util-is@1.0.2, core-util-is@~1.0.0:
    version "1.0.2"
    resolved "http://r.tnpm.oa.com/core-util-is/download/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
    integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=
  
  cosmiconfig@^5.2.1:
    version "5.2.1"
    resolved "http://r.tnpm.oa.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz#040f726809c591e77a17c0a3626ca45b4f168b1a"
    integrity sha1-BA9yaAnFked6F8CjYmykW08Wixo=
    dependencies:
      import-fresh "^2.0.0"
      is-directory "^0.3.1"
      js-yaml "^3.13.1"
      parse-json "^4.0.0"
  
  cross-spawn@^6.0.0, cross-spawn@^6.0.5:
    version "6.0.5"
    resolved "http://r.tnpm.oa.com/cross-spawn/download/cross-spawn-6.0.5.tgz#4a5ec7c64dfae22c3a14124dbacdee846d80cbc4"
    integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
    dependencies:
      nice-try "^1.0.4"
      path-key "^2.0.1"
      semver "^5.5.0"
      shebang-command "^1.2.0"
      which "^1.2.9"
  
  cross-spawn@^7.0.0:
    version "7.0.1"
    resolved "http://r.tnpm.oa.com/cross-spawn/download/cross-spawn-7.0.1.tgz#0ab56286e0f7c24e153d04cc2aa027e43a9a5d14"
    integrity sha1-CrVihuD3wk4VPQTMKqAn5DqaXRQ=
    dependencies:
      path-key "^3.1.0"
      shebang-command "^2.0.0"
      which "^2.0.1"
  
  cssom@0.3.x, "cssom@>= 0.3.2 < 0.4.0":
    version "0.3.8"
    resolved "http://r.tnpm.oa.com/cssom/download/cssom-0.3.8.tgz#9f1276f5b2b463f2114d3f2c75250af8c1a36f4a"
    integrity sha1-nxJ29bK0Y/IRTT8sdSUK+MGjb0o=
  
  cssstyle@^1.0.0:
    version "1.4.0"
    resolved "http://r.tnpm.oa.com/cssstyle/download/cssstyle-1.4.0.tgz#9d31328229d3c565c61e586b02041a28fccdccf1"
    integrity sha1-nTEyginTxWXGHlhrAgQaKPzNzPE=
    dependencies:
      cssom "0.3.x"
  
  dashdash@^1.12.0:
    version "1.14.1"
    resolved "http://r.tnpm.oa.com/dashdash/download/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
    integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
    dependencies:
      assert-plus "^1.0.0"
  
  data-urls@^1.0.0:
    version "1.1.0"
    resolved "http://r.tnpm.oa.com/data-urls/download/data-urls-1.1.0.tgz#15ee0582baa5e22bb59c77140da8f9c76963bbfe"
    integrity sha1-Fe4Fgrql4iu1nHcUDaj5x2lju/4=
    dependencies:
      abab "^2.0.0"
      whatwg-mimetype "^2.2.0"
      whatwg-url "^7.0.0"
  
  date-fns@^1.27.2:
    version "1.30.1"
    resolved "http://r.tnpm.oa.com/date-fns/download/date-fns-1.30.1.tgz#2e71bf0b119153dbb4cc4e88d9ea5acfb50dc05c"
    integrity sha1-LnG/CxGRU9u0zE6I2epaz7UNwFw=
  
  debug@^2.2.0, debug@^2.3.3:
    version "2.6.9"
    resolved "http://r.tnpm.oa.com/debug/download/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
    integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
    dependencies:
      ms "2.0.0"
  
  debug@^3.2.6:
    version "3.2.6"
    resolved "http://r.tnpm.oa.com/debug/download/debug-3.2.6.tgz#e83d17de16d8a7efb7717edbe5fb10135eee629b"
    integrity sha1-6D0X3hbYp++3cX7b5fsQE17uYps=
    dependencies:
      ms "^2.1.1"
  
  debug@^4.0.1, debug@^4.1.0, debug@^4.1.1:
    version "4.1.1"
    resolved "http://r.tnpm.oa.com/debug/download/debug-4.1.1.tgz#3b72260255109c6b589cee050f1d516139664791"
    integrity sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E=
    dependencies:
      ms "^2.1.1"
  
  decamelize@^1.2.0:
    version "1.2.0"
    resolved "http://r.tnpm.oa.com/decamelize/download/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
    integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=
  
  decode-uri-component@^0.2.0:
    version "0.2.0"
    resolved "http://r.tnpm.oa.com/decode-uri-component/download/decode-uri-component-0.2.0.tgz#eb3913333458775cb84cd1a1fae062106bb87545"
    integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=
  
  dedent@^0.7.0:
    version "0.7.0"
    resolved "http://r.tnpm.oa.com/dedent/download/dedent-0.7.0.tgz#2495ddbaf6eb874abb0e1be9df22d2e5a544326c"
    integrity sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=
  
  deep-extend@^0.6.0:
    version "0.6.0"
    resolved "http://r.tnpm.oa.com/deep-extend/download/deep-extend-0.6.0.tgz#c4fa7c95404a17a9c3e8ca7e1537312b736330ac"
    integrity sha1-xPp8lUBKF6nD6Mp+FTcxK3NjMKw=
  
  deep-is@~0.1.3:
    version "0.1.3"
    resolved "http://r.tnpm.oa.com/deep-is/download/deep-is-0.1.3.tgz#b369d6fb5dbc13eecf524f91b070feedc357cf34"
    integrity sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=
  
  define-properties@^1.1.2, define-properties@^1.1.3:
    version "1.1.3"
    resolved "http://r.tnpm.oa.com/define-properties/download/define-properties-1.1.3.tgz#cf88da6cbee26fe6db7094f61d870cbd84cee9f1"
    integrity sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=
    dependencies:
      object-keys "^1.0.12"
  
  define-property@^0.2.5:
    version "0.2.5"
    resolved "http://r.tnpm.oa.com/define-property/download/define-property-0.2.5.tgz#c35b1ef918ec3c990f9a5bc57be04aacec5c8116"
    integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
    dependencies:
      is-descriptor "^0.1.0"
  
  define-property@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/define-property/download/define-property-1.0.0.tgz#769ebaaf3f4a63aad3af9e8d304c9bbe79bfb0e6"
    integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
    dependencies:
      is-descriptor "^1.0.0"
  
  define-property@^2.0.2:
    version "2.0.2"
    resolved "http://r.tnpm.oa.com/define-property/download/define-property-2.0.2.tgz#d459689e8d654ba77e02a817f8710d702cb16e9d"
    integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
    dependencies:
      is-descriptor "^1.0.2"
      isobject "^3.0.1"
  
  del@^5.0.0:
    version "5.1.0"
    resolved "http://r.tnpm.oa.com/del/download/del-5.1.0.tgz#d9487c94e367410e6eff2925ee58c0c84a75b3a7"
    integrity sha1-2Uh8lONnQQ5u/ykl7ljAyEp1s6c=
    dependencies:
      globby "^10.0.1"
      graceful-fs "^4.2.2"
      is-glob "^4.0.1"
      is-path-cwd "^2.2.0"
      is-path-inside "^3.0.1"
      p-map "^3.0.0"
      rimraf "^3.0.0"
      slash "^3.0.0"
  
  delayed-stream@~1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
    integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=
  
  delegates@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/delegates/download/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"
    integrity sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=
  
  detect-libc@^1.0.2:
    version "1.0.3"
    resolved "http://r.tnpm.oa.com/detect-libc/download/detect-libc-1.0.3.tgz#fa137c4bd698edf55cd5cd02ac559f91a4c4ba9b"
    integrity sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=
  
  detect-newline@^2.1.0:
    version "2.1.0"
    resolved "http://r.tnpm.oa.com/detect-newline/download/detect-newline-2.1.0.tgz#f41f1c10be4b00e87b5f13da680759f2c5bfd3e2"
    integrity sha1-9B8cEL5LAOh7XxPaaAdZ8sW/0+I=
  
  diff-sequences@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/diff-sequences/download/diff-sequences-24.9.0.tgz#5715d6244e2aa65f48bba0bc972db0b0b11e95b5"
    integrity sha1-VxXWJE4qpl9Iu6C8ly2wsLEelbU=
  
  dir-glob@^3.0.1:
    version "3.0.1"
    resolved "http://r.tnpm.oa.com/dir-glob/download/dir-glob-3.0.1.tgz#56dbf73d992a4a93ba1584f4534063fd2e41717f"
    integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
    dependencies:
      path-type "^4.0.0"
  
  doctrine@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/doctrine/download/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
    integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
    dependencies:
      esutils "^2.0.2"
  
  domexception@^1.0.1:
    version "1.0.1"
    resolved "http://r.tnpm.oa.com/domexception/download/domexception-1.0.1.tgz#937442644ca6a31261ef36e3ec677fe805582c90"
    integrity sha1-k3RCZEymoxJh7zbj7Gd/6AVYLJA=
    dependencies:
      webidl-conversions "^4.0.2"
  
  ecc-jsbn@~0.1.1:
    version "0.1.2"
    resolved "http://r.tnpm.oa.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz#3a83a904e54353287874c564b7549386849a98c9"
    integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
    dependencies:
      jsbn "~0.1.0"
      safer-buffer "^2.1.0"
  
  elegant-spinner@^1.0.1:
    version "1.0.1"
    resolved "http://r.tnpm.oa.com/elegant-spinner/download/elegant-spinner-1.0.1.tgz#db043521c95d7e303fd8f345bedc3349cfb0729e"
    integrity sha1-2wQ1IcldfjA/2PNFvtwzSc+wcp4=
  
  emoji-regex@^7.0.1:
    version "7.0.3"
    resolved "http://r.tnpm.oa.com/emoji-regex/download/emoji-regex-7.0.3.tgz#933a04052860c85e83c122479c4748a8e4c72156"
    integrity sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=
  
  emoji-regex@^8.0.0:
    version "8.0.0"
    resolved "http://r.tnpm.oa.com/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
    integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=
  
  end-of-stream@^1.1.0:
    version "1.4.4"
    resolved "http://r.tnpm.oa.com/end-of-stream/download/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
    integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
    dependencies:
      once "^1.4.0"
  
  error-ex@^1.3.1:
    version "1.3.2"
    resolved "http://r.tnpm.oa.com/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
    integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
    dependencies:
      is-arrayish "^0.2.1"
  
  es-abstract@^1.5.1:
    version "1.16.2"
    resolved "http://r.tnpm.oa.com/es-abstract/download/es-abstract-1.16.2.tgz#4e874331645e9925edef141e74fc4bd144669d34"
    integrity sha1-TodDMWRemSXt7xQedPxL0URmnTQ=
    dependencies:
      es-to-primitive "^1.2.1"
      function-bind "^1.1.1"
      has "^1.0.3"
      has-symbols "^1.0.1"
      is-callable "^1.1.4"
      is-regex "^1.0.4"
      object-inspect "^1.7.0"
      object-keys "^1.1.1"
      string.prototype.trimleft "^2.1.0"
      string.prototype.trimright "^2.1.0"
  
  es-to-primitive@^1.2.1:
    version "1.2.1"
    resolved "http://r.tnpm.oa.com/es-to-primitive/download/es-to-primitive-1.2.1.tgz#e55cd4c9cdc188bcefb03b366c736323fc5c898a"
    integrity sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=
    dependencies:
      is-callable "^1.1.4"
      is-date-object "^1.0.1"
      is-symbol "^1.0.2"
  
  escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
    version "1.0.5"
    resolved "http://r.tnpm.oa.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
    integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=
  
  escodegen@^1.9.1:
    version "1.12.0"
    resolved "http://r.tnpm.oa.com/escodegen/download/escodegen-1.12.0.tgz#f763daf840af172bb3a2b6dd7219c0e17f7ff541"
    integrity sha1-92Pa+ECvFyuzorbdchnA4X9/9UE=
    dependencies:
      esprima "^3.1.3"
      estraverse "^4.2.0"
      esutils "^2.0.2"
      optionator "^0.8.1"
    optionalDependencies:
      source-map "~0.6.1"
  
  eslint-plugin-typescript@^0.14.0:
    version "0.14.0"
    resolved "http://r.tnpm.oa.com/eslint-plugin-typescript/download/eslint-plugin-typescript-0.14.0.tgz#068549c3f4c7f3f85d88d398c29fa96bf500884c"
    integrity sha1-BoVJw/TH8/hdiNOYwp+pa/UAiEw=
    dependencies:
      requireindex "~1.1.0"
  
  eslint-scope@^4.0.0:
    version "4.0.3"
    resolved "http://r.tnpm.oa.com/eslint-scope/download/eslint-scope-4.0.3.tgz#ca03833310f6889a3264781aa82e63eb9cfe7848"
    integrity sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=
    dependencies:
      esrecurse "^4.1.0"
      estraverse "^4.1.1"
  
  eslint-scope@^5.0.0:
    version "5.0.0"
    resolved "http://r.tnpm.oa.com/eslint-scope/download/eslint-scope-5.0.0.tgz#e87c8887c73e8d1ec84f1ca591645c358bfc8fb9"
    integrity sha1-6HyIh8c+jR7ITxylkWRcNYv8j7k=
    dependencies:
      esrecurse "^4.1.0"
      estraverse "^4.1.1"
  
  eslint-utils@^1.4.3:
    version "1.4.3"
    resolved "http://r.tnpm.oa.com/eslint-utils/download/eslint-utils-1.4.3.tgz#74fec7c54d0776b6f67e0251040b5806564e981f"
    integrity sha1-dP7HxU0Hdrb2fgJRBAtYBlZOmB8=
    dependencies:
      eslint-visitor-keys "^1.1.0"
  
  eslint-visitor-keys@^1.0.0, eslint-visitor-keys@^1.1.0:
    version "1.1.0"
    resolved "http://r.tnpm.oa.com/eslint-visitor-keys/download/eslint-visitor-keys-1.1.0.tgz#e2a82cea84ff246ad6fb57f9bde5b46621459ec2"
    integrity sha1-4qgs6oT/JGrW+1f5veW0ZiFFnsI=
  
  eslint@^6.7.2:
    version "6.7.2"
    resolved "http://r.tnpm.oa.com/eslint/download/eslint-6.7.2.tgz#c17707ca4ad7b2d8af986a33feba71e18a9fecd1"
    integrity sha1-wXcHykrXstivmGoz/rpx4Yqf7NE=
    dependencies:
      "@babel/code-frame" "^7.0.0"
      ajv "^6.10.0"
      chalk "^2.1.0"
      cross-spawn "^6.0.5"
      debug "^4.0.1"
      doctrine "^3.0.0"
      eslint-scope "^5.0.0"
      eslint-utils "^1.4.3"
      eslint-visitor-keys "^1.1.0"
      espree "^6.1.2"
      esquery "^1.0.1"
      esutils "^2.0.2"
      file-entry-cache "^5.0.1"
      functional-red-black-tree "^1.0.1"
      glob-parent "^5.0.0"
      globals "^12.1.0"
      ignore "^4.0.6"
      import-fresh "^3.0.0"
      imurmurhash "^0.1.4"
      inquirer "^7.0.0"
      is-glob "^4.0.0"
      js-yaml "^3.13.1"
      json-stable-stringify-without-jsonify "^1.0.1"
      levn "^0.3.0"
      lodash "^4.17.14"
      minimatch "^3.0.4"
      mkdirp "^0.5.1"
      natural-compare "^1.4.0"
      optionator "^0.8.3"
      progress "^2.0.0"
      regexpp "^2.0.1"
      semver "^6.1.2"
      strip-ansi "^5.2.0"
      strip-json-comments "^3.0.1"
      table "^5.2.3"
      text-table "^0.2.0"
      v8-compile-cache "^2.0.3"
  
  espree@^6.1.2:
    version "6.1.2"
    resolved "http://r.tnpm.oa.com/espree/download/espree-6.1.2.tgz#6c272650932b4f91c3714e5e7b5f5e2ecf47262d"
    integrity sha1-bCcmUJMrT5HDcU5ee19eLs9HJi0=
    dependencies:
      acorn "^7.1.0"
      acorn-jsx "^5.1.0"
      eslint-visitor-keys "^1.1.0"
  
  esprima@^3.1.3:
    version "3.1.3"
    resolved "http://r.tnpm.oa.com/esprima/download/esprima-3.1.3.tgz#fdca51cee6133895e3c88d535ce49dbff62a4633"
    integrity sha1-/cpRzuYTOJXjyI1TXOSdv/YqRjM=
  
  esprima@^4.0.0:
    version "4.0.1"
    resolved "http://r.tnpm.oa.com/esprima/download/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
    integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=
  
  esquery@^1.0.1:
    version "1.0.1"
    resolved "http://r.tnpm.oa.com/esquery/download/esquery-1.0.1.tgz#406c51658b1f5991a5f9b62b1dc25b00e3e5c708"
    integrity sha1-QGxRZYsfWZGl+bYrHcJbAOPlxwg=
    dependencies:
      estraverse "^4.0.0"
  
  esrecurse@^4.1.0:
    version "4.2.1"
    resolved "http://r.tnpm.oa.com/esrecurse/download/esrecurse-4.2.1.tgz#007a3b9fdbc2b3bb87e4879ea19c92fdbd3942cf"
    integrity sha1-AHo7n9vCs7uH5IeeoZyS/b05Qs8=
    dependencies:
      estraverse "^4.1.0"
  
  estraverse@^4.0.0, estraverse@^4.1.0, estraverse@^4.1.1, estraverse@^4.2.0:
    version "4.3.0"
    resolved "http://r.tnpm.oa.com/estraverse/download/estraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"
    integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=
  
  esutils@^2.0.2:
    version "2.0.3"
    resolved "http://r.tnpm.oa.com/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
    integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=
  
  exec-sh@^0.3.2:
    version "0.3.4"
    resolved "http://r.tnpm.oa.com/exec-sh/download/exec-sh-0.3.4.tgz#3a018ceb526cc6f6df2bb504b2bfe8e3a4934ec5"
    integrity sha1-OgGM61JsxvbfK7UEsr/o46STTsU=
  
  execa@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/execa/download/execa-1.0.0.tgz#c6236a5bb4df6d6f15e88e7f017798216749ddd8"
    integrity sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=
    dependencies:
      cross-spawn "^6.0.0"
      get-stream "^4.0.0"
      is-stream "^1.1.0"
      npm-run-path "^2.0.0"
      p-finally "^1.0.0"
      signal-exit "^3.0.0"
      strip-eof "^1.0.0"
  
  execa@^2.0.3:
    version "2.1.0"
    resolved "http://r.tnpm.oa.com/execa/download/execa-2.1.0.tgz#e5d3ecd837d2a60ec50f3da78fd39767747bbe99"
    integrity sha1-5dPs2DfSpg7FDz2nj9OXZ3R7vpk=
    dependencies:
      cross-spawn "^7.0.0"
      get-stream "^5.0.0"
      is-stream "^2.0.0"
      merge-stream "^2.0.0"
      npm-run-path "^3.0.0"
      onetime "^5.1.0"
      p-finally "^2.0.0"
      signal-exit "^3.0.2"
      strip-final-newline "^2.0.0"
  
  exit@^0.1.2:
    version "0.1.2"
    resolved "http://r.tnpm.oa.com/exit/download/exit-0.1.2.tgz#0632638f8d877cc82107d30a0fff1a17cba1cd0c"
    integrity sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=
  
  expand-brackets@^2.1.4:
    version "2.1.4"
    resolved "http://r.tnpm.oa.com/expand-brackets/download/expand-brackets-2.1.4.tgz#b77735e315ce30f6b6eff0f83b04151a22449622"
    integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
    dependencies:
      debug "^2.3.3"
      define-property "^0.2.5"
      extend-shallow "^2.0.1"
      posix-character-classes "^0.1.0"
      regex-not "^1.0.0"
      snapdragon "^0.8.1"
      to-regex "^3.0.1"
  
  expect@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/expect/download/expect-24.9.0.tgz#b75165b4817074fa4a157794f46fe9f1ba15b6ca"
    integrity sha1-t1FltIFwdPpKFXeU9G/p8boVtso=
    dependencies:
      "@jest/types" "^24.9.0"
      ansi-styles "^3.2.0"
      jest-get-type "^24.9.0"
      jest-matcher-utils "^24.9.0"
      jest-message-util "^24.9.0"
      jest-regex-util "^24.9.0"
  
  extend-shallow@^2.0.1:
    version "2.0.1"
    resolved "http://r.tnpm.oa.com/extend-shallow/download/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f"
    integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
    dependencies:
      is-extendable "^0.1.0"
  
  extend-shallow@^3.0.0, extend-shallow@^3.0.2:
    version "3.0.2"
    resolved "http://r.tnpm.oa.com/extend-shallow/download/extend-shallow-3.0.2.tgz#26a71aaf073b39fb2127172746131c2704028db8"
    integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
    dependencies:
      assign-symbols "^1.0.0"
      is-extendable "^1.0.1"
  
  extend@~3.0.2:
    version "3.0.2"
    resolved "http://r.tnpm.oa.com/extend/download/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa"
    integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=
  
  external-editor@^3.0.3:
    version "3.1.0"
    resolved "http://r.tnpm.oa.com/external-editor/download/external-editor-3.1.0.tgz#cb03f740befae03ea4d283caed2741a83f335495"
    integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
    dependencies:
      chardet "^0.7.0"
      iconv-lite "^0.4.24"
      tmp "^0.0.33"
  
  extglob@^2.0.4:
    version "2.0.4"
    resolved "http://r.tnpm.oa.com/extglob/download/extglob-2.0.4.tgz#ad00fe4dc612a9232e8718711dc5cb5ab0285543"
    integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
    dependencies:
      array-unique "^0.3.2"
      define-property "^1.0.0"
      expand-brackets "^2.1.4"
      extend-shallow "^2.0.1"
      fragment-cache "^0.2.1"
      regex-not "^1.0.0"
      snapdragon "^0.8.1"
      to-regex "^3.0.1"
  
  extsprintf@1.3.0:
    version "1.3.0"
    resolved "http://r.tnpm.oa.com/extsprintf/download/extsprintf-1.3.0.tgz#96918440e3041a7a414f8c52e3c574eb3c3e1e05"
    integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=
  
  extsprintf@^1.2.0:
    version "1.4.0"
    resolved "http://r.tnpm.oa.com/extsprintf/download/extsprintf-1.4.0.tgz#e2689f8f356fad62cca65a3a91c5df5f9551692f"
    integrity sha1-4mifjzVvrWLMplo6kcXfX5VRaS8=
  
  fast-deep-equal@^2.0.1:
    version "2.0.1"
    resolved "http://r.tnpm.oa.com/fast-deep-equal/download/fast-deep-equal-2.0.1.tgz#7b05218ddf9667bf7f370bf7fdb2cb15fdd0aa49"
    integrity sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk=
  
  fast-glob@^3.0.3:
    version "3.1.1"
    resolved "http://r.tnpm.oa.com/fast-glob/download/fast-glob-3.1.1.tgz#87ee30e9e9f3eb40d6f254a7997655da753d7c82"
    integrity sha1-h+4w6enz60DW8lSnmXZV2nU9fII=
    dependencies:
      "@nodelib/fs.stat" "^2.0.2"
      "@nodelib/fs.walk" "^1.2.3"
      glob-parent "^5.1.0"
      merge2 "^1.3.0"
      micromatch "^4.0.2"
  
  fast-json-stable-stringify@2.x, fast-json-stable-stringify@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.0.0.tgz#d5142c0caee6b1189f87d3a76111064f86c8bbf2"
    integrity sha1-1RQsDK7msRifh9OnYREGT4bIu/I=
  
  fast-levenshtein@~2.0.6:
    version "2.0.6"
    resolved "http://r.tnpm.oa.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
    integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=
  
  fastq@^1.6.0:
    version "1.6.0"
    resolved "http://r.tnpm.oa.com/fastq/download/fastq-1.6.0.tgz#4ec8a38f4ac25f21492673adb7eae9cfef47d1c2"
    integrity sha1-Tsijj0rCXyFJJnOtt+rpz+9H0cI=
    dependencies:
      reusify "^1.0.0"
  
  fb-watchman@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/fb-watchman/download/fb-watchman-2.0.0.tgz#54e9abf7dfa2f26cd9b1636c588c1afc05de5d58"
    integrity sha1-VOmr99+i8mzZsWNsWIwa/AXeXVg=
    dependencies:
      bser "^2.0.0"
  
  figures@^1.7.0:
    version "1.7.0"
    resolved "http://r.tnpm.oa.com/figures/download/figures-1.7.0.tgz#cbe1e3affcf1cd44b80cadfed28dc793a9701d2e"
    integrity sha1-y+Hjr/zxzUS4DK3+0o3Hk6lwHS4=
    dependencies:
      escape-string-regexp "^1.0.5"
      object-assign "^4.1.0"
  
  figures@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/figures/download/figures-2.0.0.tgz#3ab1a2d2a62c8bfb431a0c94cb797a2fce27c962"
    integrity sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=
    dependencies:
      escape-string-regexp "^1.0.5"
  
  figures@^3.0.0:
    version "3.1.0"
    resolved "http://r.tnpm.oa.com/figures/download/figures-3.1.0.tgz#4b198dd07d8d71530642864af2d45dd9e459c4ec"
    integrity sha1-SxmN0H2NcVMGQoZK8tRd2eRZxOw=
    dependencies:
      escape-string-regexp "^1.0.5"
  
  file-entry-cache@^5.0.1:
    version "5.0.1"
    resolved "http://r.tnpm.oa.com/file-entry-cache/download/file-entry-cache-5.0.1.tgz#ca0f6efa6dd3d561333fb14515065c2fafdf439c"
    integrity sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w=
    dependencies:
      flat-cache "^2.0.1"
  
  fill-range@^4.0.0:
    version "4.0.0"
    resolved "http://r.tnpm.oa.com/fill-range/download/fill-range-4.0.0.tgz#d544811d428f98eb06a63dc402d2403c328c38f7"
    integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
    dependencies:
      extend-shallow "^2.0.1"
      is-number "^3.0.0"
      repeat-string "^1.6.1"
      to-regex-range "^2.1.0"
  
  fill-range@^7.0.1:
    version "7.0.1"
    resolved "http://r.tnpm.oa.com/fill-range/download/fill-range-7.0.1.tgz#1919a6a7c75fe38b2c7c77e5198535da9acdda40"
    integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
    dependencies:
      to-regex-range "^5.0.1"
  
  find-up@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/find-up/download/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73"
    integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
    dependencies:
      locate-path "^3.0.0"
  
  find-up@^4.0.0:
    version "4.1.0"
    resolved "http://r.tnpm.oa.com/find-up/download/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
    integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
    dependencies:
      locate-path "^5.0.0"
      path-exists "^4.0.0"
  
  flat-cache@^2.0.1:
    version "2.0.1"
    resolved "http://r.tnpm.oa.com/flat-cache/download/flat-cache-2.0.1.tgz#5d296d6f04bda44a4630a301413bdbc2ec085ec0"
    integrity sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA=
    dependencies:
      flatted "^2.0.0"
      rimraf "2.6.3"
      write "1.0.3"
  
  flatted@^2.0.0:
    version "2.0.1"
    resolved "http://r.tnpm.oa.com/flatted/download/flatted-2.0.1.tgz#69e57caa8f0eacbc281d2e2cb458d46fdb449e08"
    integrity sha1-aeV8qo8OrLwoHS4stFjUb9tEngg=
  
  for-in@^1.0.2:
    version "1.0.2"
    resolved "http://r.tnpm.oa.com/for-in/download/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"
    integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=
  
  forever-agent@~0.6.1:
    version "0.6.1"
    resolved "http://r.tnpm.oa.com/forever-agent/download/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"
    integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=
  
  form-data@~2.3.2:
    version "2.3.3"
    resolved "http://r.tnpm.oa.com/form-data/download/form-data-2.3.3.tgz#dcce52c05f644f298c6a7ab936bd724ceffbf3a6"
    integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
    dependencies:
      asynckit "^0.4.0"
      combined-stream "^1.0.6"
      mime-types "^2.1.12"
  
  fragment-cache@^0.2.1:
    version "0.2.1"
    resolved "http://r.tnpm.oa.com/fragment-cache/download/fragment-cache-0.2.1.tgz#4290fad27f13e89be7f33799c6bc5a0abfff0d19"
    integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
    dependencies:
      map-cache "^0.2.2"
  
  fs-minipass@^1.2.5:
    version "1.2.7"
    resolved "http://r.tnpm.oa.com/fs-minipass/download/fs-minipass-1.2.7.tgz#ccff8570841e7fe4265693da88936c55aed7f7c7"
    integrity sha1-zP+FcIQef+QmVpPaiJNsVa7X98c=
    dependencies:
      minipass "^2.6.0"
  
  fs.realpath@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
    integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=
  
  fsevents@^1.2.7:
    version "1.2.9"
    resolved "http://r.tnpm.oa.com/fsevents/download/fsevents-1.2.9.tgz#3f5ed66583ccd6f400b5a00db6f7e861363e388f"
    integrity sha1-P17WZYPM1vQAtaANtvfoYTY+OI8=
    dependencies:
      nan "^2.12.1"
      node-pre-gyp "^0.12.0"
  
  function-bind@^1.1.1:
    version "1.1.1"
    resolved "http://r.tnpm.oa.com/function-bind/download/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"
    integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=
  
  functional-red-black-tree@^1.0.1:
    version "1.0.1"
    resolved "http://r.tnpm.oa.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz#1b0ab3bd553b2a0d6399d29c0e3ea0b252078327"
    integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=
  
  gauge@~2.7.3:
    version "2.7.4"
    resolved "http://r.tnpm.oa.com/gauge/download/gauge-2.7.4.tgz#2c03405c7538c39d7eb37b317022e325fb018bf7"
    integrity sha1-LANAXHU4w51+s3sxcCLjJfsBi/c=
    dependencies:
      aproba "^1.0.3"
      console-control-strings "^1.0.0"
      has-unicode "^2.0.0"
      object-assign "^4.1.0"
      signal-exit "^3.0.0"
      string-width "^1.0.1"
      strip-ansi "^3.0.1"
      wide-align "^1.1.0"
  
  get-caller-file@^2.0.1:
    version "2.0.5"
    resolved "http://r.tnpm.oa.com/get-caller-file/download/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"
    integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=
  
  get-own-enumerable-property-symbols@^3.0.0:
    version "3.0.1"
    resolved "http://r.tnpm.oa.com/get-own-enumerable-property-symbols/download/get-own-enumerable-property-symbols-3.0.1.tgz#6f7764f88ea11e0b514bd9bd860a132259992ca4"
    integrity sha1-b3dk+I6hHgtRS9m9hgoTIlmZLKQ=
  
  get-stdin@^7.0.0:
    version "7.0.0"
    resolved "http://r.tnpm.oa.com/get-stdin/download/get-stdin-7.0.0.tgz#8d5de98f15171a125c5e516643c7a6d0ea8a96f6"
    integrity sha1-jV3pjxUXGhJcXlFmQ8em0OqKlvY=
  
  get-stream@^4.0.0:
    version "4.1.0"
    resolved "http://r.tnpm.oa.com/get-stream/download/get-stream-4.1.0.tgz#c1b255575f3dc21d59bfc79cd3d2b46b1c3a54b5"
    integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
    dependencies:
      pump "^3.0.0"
  
  get-stream@^5.0.0:
    version "5.1.0"
    resolved "http://r.tnpm.oa.com/get-stream/download/get-stream-5.1.0.tgz#01203cdc92597f9b909067c3e656cc1f4d3c4dc9"
    integrity sha1-ASA83JJZf5uQkGfD5lbMH008Tck=
    dependencies:
      pump "^3.0.0"
  
  get-value@^2.0.3, get-value@^2.0.6:
    version "2.0.6"
    resolved "http://r.tnpm.oa.com/get-value/download/get-value-2.0.6.tgz#dc15ca1c672387ca76bd37ac0a395ba2042a2c28"
    integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=
  
  getpass@^0.1.1:
    version "0.1.7"
    resolved "http://r.tnpm.oa.com/getpass/download/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
    integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
    dependencies:
      assert-plus "^1.0.0"
  
  glob-parent@^5.0.0, glob-parent@^5.1.0:
    version "5.1.0"
    resolved "http://r.tnpm.oa.com/glob-parent/download/glob-parent-5.1.0.tgz#5f4c1d1e748d30cd73ad2944b3577a81b081e8c2"
    integrity sha1-X0wdHnSNMM1zrSlEs1d6gbCB6MI=
    dependencies:
      is-glob "^4.0.1"
  
  glob@^7.1.1, glob@^7.1.2, glob@^7.1.3, glob@^7.1.6:
    version "7.1.6"
    resolved "http://r.tnpm.oa.com/glob/download/glob-7.1.6.tgz#141f33b81a7c2492e125594307480c46679278a6"
    integrity sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY=
    dependencies:
      fs.realpath "^1.0.0"
      inflight "^1.0.4"
      inherits "2"
      minimatch "^3.0.4"
      once "^1.3.0"
      path-is-absolute "^1.0.0"
  
  globals@^11.1.0:
    version "11.12.0"
    resolved "http://r.tnpm.oa.com/globals/download/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
    integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=
  
  globals@^12.1.0:
    version "12.3.0"
    resolved "http://r.tnpm.oa.com/globals/download/globals-12.3.0.tgz#1e564ee5c4dded2ab098b0f88f24702a3c56be13"
    integrity sha1-HlZO5cTd7SqwmLD4jyRwKjxWvhM=
    dependencies:
      type-fest "^0.8.1"
  
  globby@^10.0.1:
    version "10.0.1"
    resolved "http://r.tnpm.oa.com/globby/download/globby-10.0.1.tgz#4782c34cb75dd683351335c5829cc3420e606b22"
    integrity sha1-R4LDTLdd1oM1EzXFgpzDQg5gayI=
    dependencies:
      "@types/glob" "^7.1.1"
      array-union "^2.1.0"
      dir-glob "^3.0.1"
      fast-glob "^3.0.3"
      glob "^7.1.3"
      ignore "^5.1.1"
      merge2 "^1.2.3"
      slash "^3.0.0"
  
  graceful-fs@^4.1.11, graceful-fs@^4.1.15, graceful-fs@^4.1.2, graceful-fs@^4.2.2:
    version "4.2.3"
    resolved "http://r.tnpm.oa.com/graceful-fs/download/graceful-fs-4.2.3.tgz#4a12ff1b60376ef09862c2093edd908328be8423"
    integrity sha1-ShL/G2A3bvCYYsIJPt2Qgyi+hCM=
  
  growly@^1.3.0:
    version "1.3.0"
    resolved "http://r.tnpm.oa.com/growly/download/growly-1.3.0.tgz#f10748cbe76af964b7c96c93c6bcc28af120c081"
    integrity sha1-8QdIy+dq+WS3yWyTxrzCivEgwIE=
  
  handlebars@^4.1.2:
    version "4.5.3"
    resolved "http://r.tnpm.oa.com/handlebars/download/handlebars-4.5.3.tgz#5cf75bd8714f7605713511a56be7c349becb0482"
    integrity sha1-XPdb2HFPdgVxNRGla+fDSb7LBII=
    dependencies:
      neo-async "^2.6.0"
      optimist "^0.6.1"
      source-map "^0.6.1"
    optionalDependencies:
      uglify-js "^3.1.4"
  
  har-schema@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/har-schema/download/har-schema-2.0.0.tgz#a94c2224ebcac04782a0d9035521f24735b7ec92"
    integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=
  
  har-validator@~5.1.0:
    version "5.1.3"
    resolved "http://r.tnpm.oa.com/har-validator/download/har-validator-5.1.3.tgz#1ef89ebd3e4996557675eed9893110dc350fa080"
    integrity sha1-HvievT5JllV2de7ZiTEQ3DUPoIA=
    dependencies:
      ajv "^6.5.5"
      har-schema "^2.0.0"
  
  has-ansi@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/has-ansi/download/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
    integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
    dependencies:
      ansi-regex "^2.0.0"
  
  has-flag@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
    integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=
  
  has-symbols@^1.0.1:
    version "1.0.1"
    resolved "http://r.tnpm.oa.com/has-symbols/download/has-symbols-1.0.1.tgz#9f5214758a44196c406d9bd76cebf81ec2dd31e8"
    integrity sha1-n1IUdYpEGWxAbZvXbOv4HsLdMeg=
  
  has-unicode@^2.0.0:
    version "2.0.1"
    resolved "http://r.tnpm.oa.com/has-unicode/download/has-unicode-2.0.1.tgz#e0e6fe6a28cf51138855e086d1691e771de2a8b9"
    integrity sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=
  
  has-value@^0.3.1:
    version "0.3.1"
    resolved "http://r.tnpm.oa.com/has-value/download/has-value-0.3.1.tgz#7b1f58bada62ca827ec0a2078025654845995e1f"
    integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
    dependencies:
      get-value "^2.0.3"
      has-values "^0.1.4"
      isobject "^2.0.0"
  
  has-value@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/has-value/download/has-value-1.0.0.tgz#18b281da585b1c5c51def24c930ed29a0be6b177"
    integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
    dependencies:
      get-value "^2.0.6"
      has-values "^1.0.0"
      isobject "^3.0.0"
  
  has-values@^0.1.4:
    version "0.1.4"
    resolved "http://r.tnpm.oa.com/has-values/download/has-values-0.1.4.tgz#6d61de95d91dfca9b9a02089ad384bff8f62b771"
    integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=
  
  has-values@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/has-values/download/has-values-1.0.0.tgz#95b0b63fec2146619a6fe57fe75628d5a39efe4f"
    integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
    dependencies:
      is-number "^3.0.0"
      kind-of "^4.0.0"
  
  has@^1.0.1, has@^1.0.3:
    version "1.0.3"
    resolved "http://r.tnpm.oa.com/has/download/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
    integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
    dependencies:
      function-bind "^1.1.1"
  
  hosted-git-info@^2.1.4:
    version "2.8.5"
    resolved "http://r.tnpm.oa.com/hosted-git-info/download/hosted-git-info-2.8.5.tgz#759cfcf2c4d156ade59b0b2dfabddc42a6b9c70c"
    integrity sha1-dZz88sTRVq3lmwst+r3cQqa5xww=
  
  html-encoding-sniffer@^1.0.2:
    version "1.0.2"
    resolved "http://r.tnpm.oa.com/html-encoding-sniffer/download/html-encoding-sniffer-1.0.2.tgz#e70d84b94da53aa375e11fe3a351be6642ca46f8"
    integrity sha1-5w2EuU2lOqN14R/jo1G+ZkLKRvg=
    dependencies:
      whatwg-encoding "^1.0.1"
  
  http-signature@~1.2.0:
    version "1.2.0"
    resolved "http://r.tnpm.oa.com/http-signature/download/http-signature-1.2.0.tgz#9aecd925114772f3d95b65a60abb8f7c18fbace1"
    integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
    dependencies:
      assert-plus "^1.0.0"
      jsprim "^1.2.2"
      sshpk "^1.7.0"
  
  husky@^3.1.0:
    version "3.1.0"
    resolved "http://r.tnpm.oa.com/husky/download/husky-3.1.0.tgz#5faad520ab860582ed94f0c1a77f0f04c90b57c0"
    integrity sha1-X6rVIKuGBYLtlPDBp38PBMkLV8A=
    dependencies:
      chalk "^2.4.2"
      ci-info "^2.0.0"
      cosmiconfig "^5.2.1"
      execa "^1.0.0"
      get-stdin "^7.0.0"
      opencollective-postinstall "^2.0.2"
      pkg-dir "^4.2.0"
      please-upgrade-node "^3.2.0"
      read-pkg "^5.2.0"
      run-node "^1.0.0"
      slash "^3.0.0"
  
  iconv-lite@0.4.24, iconv-lite@^0.4.24, iconv-lite@^0.4.4:
    version "0.4.24"
    resolved "http://r.tnpm.oa.com/iconv-lite/download/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
    integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
    dependencies:
      safer-buffer ">= 2.1.2 < 3"
  
  ignore-walk@^3.0.1:
    version "3.0.3"
    resolved "http://r.tnpm.oa.com/ignore-walk/download/ignore-walk-3.0.3.tgz#017e2447184bfeade7c238e4aefdd1e8f95b1e37"
    integrity sha1-AX4kRxhL/q3nwjjkrv3R6PlbHjc=
    dependencies:
      minimatch "^3.0.4"
  
  ignore@^4.0.6:
    version "4.0.6"
    resolved "http://r.tnpm.oa.com/ignore/download/ignore-4.0.6.tgz#750e3db5862087b4737ebac8207ffd1ef27b25fc"
    integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=
  
  ignore@^5.1.1:
    version "5.1.4"
    resolved "http://r.tnpm.oa.com/ignore/download/ignore-5.1.4.tgz#84b7b3dbe64552b6ef0eca99f6743dbec6d97adf"
    integrity sha1-hLez2+ZFUrbvDsqZ9nQ9vsbZet8=
  
  import-fresh@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/import-fresh/download/import-fresh-2.0.0.tgz#d81355c15612d386c61f9ddd3922d4304822a546"
    integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
    dependencies:
      caller-path "^2.0.0"
      resolve-from "^3.0.0"
  
  import-fresh@^3.0.0:
    version "3.2.1"
    resolved "http://r.tnpm.oa.com/import-fresh/download/import-fresh-3.2.1.tgz#633ff618506e793af5ac91bf48b72677e15cbe66"
    integrity sha1-Yz/2GFBueTr1rJG/SLcmd+FcvmY=
    dependencies:
      parent-module "^1.0.0"
      resolve-from "^4.0.0"
  
  import-local@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/import-local/download/import-local-2.0.0.tgz#55070be38a5993cf18ef6db7e961f5bee5c5a09d"
    integrity sha1-VQcL44pZk88Y72236WH1vuXFoJ0=
    dependencies:
      pkg-dir "^3.0.0"
      resolve-cwd "^2.0.0"
  
  imurmurhash@^0.1.4:
    version "0.1.4"
    resolved "http://r.tnpm.oa.com/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
    integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=
  
  indent-string@^3.0.0:
    version "3.2.0"
    resolved "http://r.tnpm.oa.com/indent-string/download/indent-string-3.2.0.tgz#4a5fd6d27cc332f37e5419a504dbb837105c9289"
    integrity sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok=
  
  indent-string@^4.0.0:
    version "4.0.0"
    resolved "http://r.tnpm.oa.com/indent-string/download/indent-string-4.0.0.tgz#624f8f4497d619b2d9768531d58f4122854d7251"
    integrity sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=
  
  inflight@^1.0.4:
    version "1.0.6"
    resolved "http://r.tnpm.oa.com/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
    integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
    dependencies:
      once "^1.3.0"
      wrappy "1"
  
  inherits@2, inherits@~2.0.3:
    version "2.0.4"
    resolved "http://r.tnpm.oa.com/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
    integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=
  
  ini@~1.3.0:
    version "1.3.5"
    resolved "http://r.tnpm.oa.com/ini/download/ini-1.3.5.tgz#eee25f56db1c9ec6085e0c22778083f596abf927"
    integrity sha1-7uJfVtscnsYIXgwid4CD9Zar+Sc=
  
  inquirer@^7.0.0:
    version "7.0.0"
    resolved "http://r.tnpm.oa.com/inquirer/download/inquirer-7.0.0.tgz#9e2b032dde77da1db5db804758b8fea3a970519a"
    integrity sha1-nisDLd532h2124BHWLj+o6lwUZo=
    dependencies:
      ansi-escapes "^4.2.1"
      chalk "^2.4.2"
      cli-cursor "^3.1.0"
      cli-width "^2.0.0"
      external-editor "^3.0.3"
      figures "^3.0.0"
      lodash "^4.17.15"
      mute-stream "0.0.8"
      run-async "^2.2.0"
      rxjs "^6.4.0"
      string-width "^4.1.0"
      strip-ansi "^5.1.0"
      through "^2.3.6"
  
  invariant@^2.2.4:
    version "2.2.4"
    resolved "http://r.tnpm.oa.com/invariant/download/invariant-2.2.4.tgz#610f3c92c9359ce1db616e538008d23ff35158e6"
    integrity sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=
    dependencies:
      loose-envify "^1.0.0"
  
  is-accessor-descriptor@^0.1.6:
    version "0.1.6"
    resolved "http://r.tnpm.oa.com/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz#a9e12cb3ae8d876727eeef3843f8a0897b5c98d6"
    integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
    dependencies:
      kind-of "^3.0.2"
  
  is-accessor-descriptor@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz#169c2f6d3df1f992618072365c9b0ea1f6878656"
    integrity sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=
    dependencies:
      kind-of "^6.0.0"
  
  is-arrayish@^0.2.1:
    version "0.2.1"
    resolved "http://r.tnpm.oa.com/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
    integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=
  
  is-buffer@^1.1.5:
    version "1.1.6"
    resolved "http://r.tnpm.oa.com/is-buffer/download/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
    integrity sha1-76ouqdqg16suoTqXsritUf776L4=
  
  is-callable@^1.1.4:
    version "1.1.4"
    resolved "http://r.tnpm.oa.com/is-callable/download/is-callable-1.1.4.tgz#1e1adf219e1eeb684d691f9d6a05ff0d30a24d75"
    integrity sha1-HhrfIZ4e62hNaR+dagX/DTCiTXU=
  
  is-ci@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/is-ci/download/is-ci-2.0.0.tgz#6bc6334181810e04b5c22b3d589fdca55026404c"
    integrity sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw=
    dependencies:
      ci-info "^2.0.0"
  
  is-data-descriptor@^0.1.4:
    version "0.1.4"
    resolved "http://r.tnpm.oa.com/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz#0b5ee648388e2c860282e793f1856fec3f301b56"
    integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
    dependencies:
      kind-of "^3.0.2"
  
  is-data-descriptor@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz#d84876321d0e7add03990406abbbbd36ba9268c7"
    integrity sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=
    dependencies:
      kind-of "^6.0.0"
  
  is-date-object@^1.0.1:
    version "1.0.1"
    resolved "http://r.tnpm.oa.com/is-date-object/download/is-date-object-1.0.1.tgz#9aa20eb6aeebbff77fbd33e74ca01b33581d3a16"
    integrity sha1-mqIOtq7rv/d/vTPnTKAbM1gdOhY=
  
  is-descriptor@^0.1.0:
    version "0.1.6"
    resolved "http://r.tnpm.oa.com/is-descriptor/download/is-descriptor-0.1.6.tgz#366d8240dde487ca51823b1ab9f07a10a78251ca"
    integrity sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=
    dependencies:
      is-accessor-descriptor "^0.1.6"
      is-data-descriptor "^0.1.4"
      kind-of "^5.0.0"
  
  is-descriptor@^1.0.0, is-descriptor@^1.0.2:
    version "1.0.2"
    resolved "http://r.tnpm.oa.com/is-descriptor/download/is-descriptor-1.0.2.tgz#3b159746a66604b04f8c81524ba365c5f14d86ec"
    integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
    dependencies:
      is-accessor-descriptor "^1.0.0"
      is-data-descriptor "^1.0.0"
      kind-of "^6.0.2"
  
  is-directory@^0.3.1:
    version "0.3.1"
    resolved "http://r.tnpm.oa.com/is-directory/download/is-directory-0.3.1.tgz#61339b6f2475fc772fd9c9d83f5c8575dc154ae1"
    integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=
  
  is-extendable@^0.1.0, is-extendable@^0.1.1:
    version "0.1.1"
    resolved "http://r.tnpm.oa.com/is-extendable/download/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"
    integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=
  
  is-extendable@^1.0.1:
    version "1.0.1"
    resolved "http://r.tnpm.oa.com/is-extendable/download/is-extendable-1.0.1.tgz#a7470f9e426733d81bd81e1155264e3a3507cab4"
    integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
    dependencies:
      is-plain-object "^2.0.4"
  
  is-extglob@^2.1.1:
    version "2.1.1"
    resolved "http://r.tnpm.oa.com/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
    integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=
  
  is-fullwidth-code-point@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz#ef9e31386f031a7f0d643af82fde50c457ef00cb"
    integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
    dependencies:
      number-is-nan "^1.0.0"
  
  is-fullwidth-code-point@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"
    integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=
  
  is-fullwidth-code-point@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
    integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=
  
  is-generator-fn@^2.0.0:
    version "2.1.0"
    resolved "http://r.tnpm.oa.com/is-generator-fn/download/is-generator-fn-2.1.0.tgz#7d140adc389aaf3011a8f2a2a4cfa6faadffb118"
    integrity sha1-fRQK3DiarzARqPKipM+m+q3/sRg=
  
  is-glob@^4.0.0, is-glob@^4.0.1:
    version "4.0.1"
    resolved "http://r.tnpm.oa.com/is-glob/download/is-glob-4.0.1.tgz#7567dbe9f2f5e2467bc77ab83c4a29482407a5dc"
    integrity sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw=
    dependencies:
      is-extglob "^2.1.1"
  
  is-number@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/is-number/download/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
    integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
    dependencies:
      kind-of "^3.0.2"
  
  is-number@^7.0.0:
    version "7.0.0"
    resolved "http://r.tnpm.oa.com/is-number/download/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
    integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=
  
  is-obj@^1.0.1:
    version "1.0.1"
    resolved "http://r.tnpm.oa.com/is-obj/download/is-obj-1.0.1.tgz#3e4729ac1f5fde025cd7d83a896dab9f4f67db0f"
    integrity sha1-PkcprB9f3gJc19g6iW2rn09n2w8=
  
  is-observable@^1.1.0:
    version "1.1.0"
    resolved "http://r.tnpm.oa.com/is-observable/download/is-observable-1.1.0.tgz#b3e986c8f44de950867cab5403f5a3465005975e"
    integrity sha1-s+mGyPRN6VCGfKtUA/WjRlAFl14=
    dependencies:
      symbol-observable "^1.1.0"
  
  is-path-cwd@^2.2.0:
    version "2.2.0"
    resolved "http://r.tnpm.oa.com/is-path-cwd/download/is-path-cwd-2.2.0.tgz#67d43b82664a7b5191fd9119127eb300048a9fdb"
    integrity sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s=
  
  is-path-inside@^3.0.1:
    version "3.0.2"
    resolved "http://r.tnpm.oa.com/is-path-inside/download/is-path-inside-3.0.2.tgz#f5220fc82a3e233757291dddc9c5877f2a1f3017"
    integrity sha1-9SIPyCo+IzdXKR3dycWHfyofMBc=
  
  is-plain-object@^2.0.3, is-plain-object@^2.0.4:
    version "2.0.4"
    resolved "http://r.tnpm.oa.com/is-plain-object/download/is-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677"
    integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
    dependencies:
      isobject "^3.0.1"
  
  is-promise@^2.1.0:
    version "2.1.0"
    resolved "http://r.tnpm.oa.com/is-promise/download/is-promise-2.1.0.tgz#79a2a9ece7f096e80f36d2b2f3bc16c1ff4bf3fa"
    integrity sha1-eaKp7OfwlugPNtKy87wWwf9L8/o=
  
  is-regex@^1.0.4:
    version "1.0.4"
    resolved "http://r.tnpm.oa.com/is-regex/download/is-regex-1.0.4.tgz#5517489b547091b0930e095654ced25ee97e9491"
    integrity sha1-VRdIm1RwkbCTDglWVM7SXul+lJE=
    dependencies:
      has "^1.0.1"
  
  is-regexp@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/is-regexp/download/is-regexp-1.0.0.tgz#fd2d883545c46bac5a633e7b9a09e87fa2cb5069"
    integrity sha1-/S2INUXEa6xaYz57mgnof6LLUGk=
  
  is-stream@^1.1.0:
    version "1.1.0"
    resolved "http://r.tnpm.oa.com/is-stream/download/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"
    integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=
  
  is-stream@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/is-stream/download/is-stream-2.0.0.tgz#bde9c32680d6fae04129d6ac9d921ce7815f78e3"
    integrity sha1-venDJoDW+uBBKdasnZIc54FfeOM=
  
  is-symbol@^1.0.2:
    version "1.0.3"
    resolved "http://r.tnpm.oa.com/is-symbol/download/is-symbol-1.0.3.tgz#38e1014b9e6329be0de9d24a414fd7441ec61937"
    integrity sha1-OOEBS55jKb4N6dJKQU/XRB7GGTc=
    dependencies:
      has-symbols "^1.0.1"
  
  is-typedarray@~1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/is-typedarray/download/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"
    integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=
  
  is-windows@^1.0.2:
    version "1.0.2"
    resolved "http://r.tnpm.oa.com/is-windows/download/is-windows-1.0.2.tgz#d1850eb9791ecd18e6182ce12a30f396634bb19d"
    integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=
  
  is-wsl@^1.1.0:
    version "1.1.0"
    resolved "http://r.tnpm.oa.com/is-wsl/download/is-wsl-1.1.0.tgz#1f16e4aa22b04d1336b66188a66af3c600c3a66d"
    integrity sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=
  
  isarray@1.0.0, isarray@~1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
    integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=
  
  isexe@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
    integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=
  
  isobject@^2.0.0:
    version "2.1.0"
    resolved "http://r.tnpm.oa.com/isobject/download/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
    integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
    dependencies:
      isarray "1.0.0"
  
  isobject@^3.0.0, isobject@^3.0.1:
    version "3.0.1"
    resolved "http://r.tnpm.oa.com/isobject/download/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"
    integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=
  
  isstream@~0.1.2:
    version "0.1.2"
    resolved "http://r.tnpm.oa.com/isstream/download/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"
    integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=
  
  istanbul-lib-coverage@^2.0.2, istanbul-lib-coverage@^2.0.5:
    version "2.0.5"
    resolved "http://r.tnpm.oa.com/istanbul-lib-coverage/download/istanbul-lib-coverage-2.0.5.tgz#675f0ab69503fad4b1d849f736baaca803344f49"
    integrity sha1-Z18KtpUD+tSx2En3NrqsqAM0T0k=
  
  istanbul-lib-instrument@^3.0.1, istanbul-lib-instrument@^3.3.0:
    version "3.3.0"
    resolved "http://r.tnpm.oa.com/istanbul-lib-instrument/download/istanbul-lib-instrument-3.3.0.tgz#a5f63d91f0bbc0c3e479ef4c5de027335ec6d630"
    integrity sha1-pfY9kfC7wMPkee9MXeAnM17G1jA=
    dependencies:
      "@babel/generator" "^7.4.0"
      "@babel/parser" "^7.4.3"
      "@babel/template" "^7.4.0"
      "@babel/traverse" "^7.4.3"
      "@babel/types" "^7.4.0"
      istanbul-lib-coverage "^2.0.5"
      semver "^6.0.0"
  
  istanbul-lib-report@^2.0.4:
    version "2.0.8"
    resolved "http://r.tnpm.oa.com/istanbul-lib-report/download/istanbul-lib-report-2.0.8.tgz#5a8113cd746d43c4889eba36ab10e7d50c9b4f33"
    integrity sha1-WoETzXRtQ8SInro2qxDn1QybTzM=
    dependencies:
      istanbul-lib-coverage "^2.0.5"
      make-dir "^2.1.0"
      supports-color "^6.1.0"
  
  istanbul-lib-source-maps@^3.0.1:
    version "3.0.6"
    resolved "http://r.tnpm.oa.com/istanbul-lib-source-maps/download/istanbul-lib-source-maps-3.0.6.tgz#284997c48211752ec486253da97e3879defba8c8"
    integrity sha1-KEmXxIIRdS7EhiU9qX44ed77qMg=
    dependencies:
      debug "^4.1.1"
      istanbul-lib-coverage "^2.0.5"
      make-dir "^2.1.0"
      rimraf "^2.6.3"
      source-map "^0.6.1"
  
  istanbul-reports@^2.2.6:
    version "2.2.6"
    resolved "http://r.tnpm.oa.com/istanbul-reports/download/istanbul-reports-2.2.6.tgz#7b4f2660d82b29303a8fe6091f8ca4bf058da1af"
    integrity sha1-e08mYNgrKTA6j+YJH4ykvwWNoa8=
    dependencies:
      handlebars "^4.1.2"
  
  jest-changed-files@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-changed-files/download/jest-changed-files-24.9.0.tgz#08d8c15eb79a7fa3fc98269bc14b451ee82f8039"
    integrity sha1-CNjBXreaf6P8mCabwUtFHugvgDk=
    dependencies:
      "@jest/types" "^24.9.0"
      execa "^1.0.0"
      throat "^4.0.0"
  
  jest-cli@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-cli/download/jest-cli-24.9.0.tgz#ad2de62d07472d419c6abc301fc432b98b10d2af"
    integrity sha1-rS3mLQdHLUGcarwwH8QyuYsQ0q8=
    dependencies:
      "@jest/core" "^24.9.0"
      "@jest/test-result" "^24.9.0"
      "@jest/types" "^24.9.0"
      chalk "^2.0.1"
      exit "^0.1.2"
      import-local "^2.0.0"
      is-ci "^2.0.0"
      jest-config "^24.9.0"
      jest-util "^24.9.0"
      jest-validate "^24.9.0"
      prompts "^2.0.1"
      realpath-native "^1.1.0"
      yargs "^13.3.0"
  
  jest-config@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-config/download/jest-config-24.9.0.tgz#fb1bbc60c73a46af03590719efa4825e6e4dd1b5"
    integrity sha1-+xu8YMc6Rq8DWQcZ76SCXm5N0bU=
    dependencies:
      "@babel/core" "^7.1.0"
      "@jest/test-sequencer" "^24.9.0"
      "@jest/types" "^24.9.0"
      babel-jest "^24.9.0"
      chalk "^2.0.1"
      glob "^7.1.1"
      jest-environment-jsdom "^24.9.0"
      jest-environment-node "^24.9.0"
      jest-get-type "^24.9.0"
      jest-jasmine2 "^24.9.0"
      jest-regex-util "^24.3.0"
      jest-resolve "^24.9.0"
      jest-util "^24.9.0"
      jest-validate "^24.9.0"
      micromatch "^3.1.10"
      pretty-format "^24.9.0"
      realpath-native "^1.1.0"
  
  jest-diff@^24.3.0, jest-diff@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-diff/download/jest-diff-24.9.0.tgz#931b7d0d5778a1baf7452cb816e325e3724055da"
    integrity sha1-kxt9DVd4obr3RSy4FuMl43JAVdo=
    dependencies:
      chalk "^2.0.1"
      diff-sequences "^24.9.0"
      jest-get-type "^24.9.0"
      pretty-format "^24.9.0"
  
  jest-docblock@^24.3.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-docblock/download/jest-docblock-24.9.0.tgz#7970201802ba560e1c4092cc25cbedf5af5a8ce2"
    integrity sha1-eXAgGAK6Vg4cQJLMJcvt9a9ajOI=
    dependencies:
      detect-newline "^2.1.0"
  
  jest-each@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-each/download/jest-each-24.9.0.tgz#eb2da602e2a610898dbc5f1f6df3ba86b55f8b05"
    integrity sha1-6y2mAuKmEImNvF8fbfO6hrVfiwU=
    dependencies:
      "@jest/types" "^24.9.0"
      chalk "^2.0.1"
      jest-get-type "^24.9.0"
      jest-util "^24.9.0"
      pretty-format "^24.9.0"
  
  jest-environment-jsdom@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-environment-jsdom/download/jest-environment-jsdom-24.9.0.tgz#4b0806c7fc94f95edb369a69cc2778eec2b7375b"
    integrity sha1-SwgGx/yU+V7bNpppzCd47sK3N1s=
    dependencies:
      "@jest/environment" "^24.9.0"
      "@jest/fake-timers" "^24.9.0"
      "@jest/types" "^24.9.0"
      jest-mock "^24.9.0"
      jest-util "^24.9.0"
      jsdom "^11.5.1"
  
  jest-environment-node@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-environment-node/download/jest-environment-node-24.9.0.tgz#333d2d2796f9687f2aeebf0742b519f33c1cbfd3"
    integrity sha1-Mz0tJ5b5aH8q7r8HQrUZ8zwcv9M=
    dependencies:
      "@jest/environment" "^24.9.0"
      "@jest/fake-timers" "^24.9.0"
      "@jest/types" "^24.9.0"
      jest-mock "^24.9.0"
      jest-util "^24.9.0"
  
  jest-get-type@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-get-type/download/jest-get-type-24.9.0.tgz#1684a0c8a50f2e4901b6644ae861f579eed2ef0e"
    integrity sha1-FoSgyKUPLkkBtmRK6GH1ee7S7w4=
  
  jest-haste-map@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-haste-map/download/jest-haste-map-24.9.0.tgz#b38a5d64274934e21fa417ae9a9fbeb77ceaac7d"
    integrity sha1-s4pdZCdJNOIfpBeump++t3zqrH0=
    dependencies:
      "@jest/types" "^24.9.0"
      anymatch "^2.0.0"
      fb-watchman "^2.0.0"
      graceful-fs "^4.1.15"
      invariant "^2.2.4"
      jest-serializer "^24.9.0"
      jest-util "^24.9.0"
      jest-worker "^24.9.0"
      micromatch "^3.1.10"
      sane "^4.0.3"
      walker "^1.0.7"
    optionalDependencies:
      fsevents "^1.2.7"
  
  jest-jasmine2@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-jasmine2/download/jest-jasmine2-24.9.0.tgz#1f7b1bd3242c1774e62acabb3646d96afc3be6a0"
    integrity sha1-H3sb0yQsF3TmKsq7NkbZavw75qA=
    dependencies:
      "@babel/traverse" "^7.1.0"
      "@jest/environment" "^24.9.0"
      "@jest/test-result" "^24.9.0"
      "@jest/types" "^24.9.0"
      chalk "^2.0.1"
      co "^4.6.0"
      expect "^24.9.0"
      is-generator-fn "^2.0.0"
      jest-each "^24.9.0"
      jest-matcher-utils "^24.9.0"
      jest-message-util "^24.9.0"
      jest-runtime "^24.9.0"
      jest-snapshot "^24.9.0"
      jest-util "^24.9.0"
      pretty-format "^24.9.0"
      throat "^4.0.0"
  
  jest-leak-detector@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-leak-detector/download/jest-leak-detector-24.9.0.tgz#b665dea7c77100c5c4f7dfcb153b65cf07dcf96a"
    integrity sha1-tmXep8dxAMXE99/LFTtlzwfc+Wo=
    dependencies:
      jest-get-type "^24.9.0"
      pretty-format "^24.9.0"
  
  jest-matcher-utils@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-matcher-utils/download/jest-matcher-utils-24.9.0.tgz#f5b3661d5e628dffe6dd65251dfdae0e87c3a073"
    integrity sha1-9bNmHV5ijf/m3WUlHf2uDofDoHM=
    dependencies:
      chalk "^2.0.1"
      jest-diff "^24.9.0"
      jest-get-type "^24.9.0"
      pretty-format "^24.9.0"
  
  jest-message-util@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-message-util/download/jest-message-util-24.9.0.tgz#527f54a1e380f5e202a8d1149b0ec872f43119e3"
    integrity sha1-Un9UoeOA9eICqNEUmw7IcvQxGeM=
    dependencies:
      "@babel/code-frame" "^7.0.0"
      "@jest/test-result" "^24.9.0"
      "@jest/types" "^24.9.0"
      "@types/stack-utils" "^1.0.1"
      chalk "^2.0.1"
      micromatch "^3.1.10"
      slash "^2.0.0"
      stack-utils "^1.0.1"
  
  jest-mock@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-mock/download/jest-mock-24.9.0.tgz#c22835541ee379b908673ad51087a2185c13f1c6"
    integrity sha1-wig1VB7jebkIZzrVEIeiGFwT8cY=
    dependencies:
      "@jest/types" "^24.9.0"
  
  jest-pnp-resolver@^1.2.1:
    version "1.2.1"
    resolved "http://r.tnpm.oa.com/jest-pnp-resolver/download/jest-pnp-resolver-1.2.1.tgz#ecdae604c077a7fbc70defb6d517c3c1c898923a"
    integrity sha1-7NrmBMB3p/vHDe+21RfDwciYkjo=
  
  jest-regex-util@^24.3.0, jest-regex-util@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-regex-util/download/jest-regex-util-24.9.0.tgz#c13fb3380bde22bf6575432c493ea8fe37965636"
    integrity sha1-wT+zOAveIr9ldUMsST6o/jeWVjY=
  
  jest-resolve-dependencies@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-resolve-dependencies/download/jest-resolve-dependencies-24.9.0.tgz#ad055198959c4cfba8a4f066c673a3f0786507ab"
    integrity sha1-rQVRmJWcTPuopPBmxnOj8HhlB6s=
    dependencies:
      "@jest/types" "^24.9.0"
      jest-regex-util "^24.3.0"
      jest-snapshot "^24.9.0"
  
  jest-resolve@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-resolve/download/jest-resolve-24.9.0.tgz#dff04c7687af34c4dd7e524892d9cf77e5d17321"
    integrity sha1-3/BMdoevNMTdflJIktnPd+XRcyE=
    dependencies:
      "@jest/types" "^24.9.0"
      browser-resolve "^1.11.3"
      chalk "^2.0.1"
      jest-pnp-resolver "^1.2.1"
      realpath-native "^1.1.0"
  
  jest-runner@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-runner/download/jest-runner-24.9.0.tgz#574fafdbd54455c2b34b4bdf4365a23857fcdf42"
    integrity sha1-V0+v29VEVcKzS0vfQ2WiOFf830I=
    dependencies:
      "@jest/console" "^24.7.1"
      "@jest/environment" "^24.9.0"
      "@jest/test-result" "^24.9.0"
      "@jest/types" "^24.9.0"
      chalk "^2.4.2"
      exit "^0.1.2"
      graceful-fs "^4.1.15"
      jest-config "^24.9.0"
      jest-docblock "^24.3.0"
      jest-haste-map "^24.9.0"
      jest-jasmine2 "^24.9.0"
      jest-leak-detector "^24.9.0"
      jest-message-util "^24.9.0"
      jest-resolve "^24.9.0"
      jest-runtime "^24.9.0"
      jest-util "^24.9.0"
      jest-worker "^24.6.0"
      source-map-support "^0.5.6"
      throat "^4.0.0"
  
  jest-runtime@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-runtime/download/jest-runtime-24.9.0.tgz#9f14583af6a4f7314a6a9d9f0226e1a781c8e4ac"
    integrity sha1-nxRYOvak9zFKap2fAibhp4HI5Kw=
    dependencies:
      "@jest/console" "^24.7.1"
      "@jest/environment" "^24.9.0"
      "@jest/source-map" "^24.3.0"
      "@jest/transform" "^24.9.0"
      "@jest/types" "^24.9.0"
      "@types/yargs" "^13.0.0"
      chalk "^2.0.1"
      exit "^0.1.2"
      glob "^7.1.3"
      graceful-fs "^4.1.15"
      jest-config "^24.9.0"
      jest-haste-map "^24.9.0"
      jest-message-util "^24.9.0"
      jest-mock "^24.9.0"
      jest-regex-util "^24.3.0"
      jest-resolve "^24.9.0"
      jest-snapshot "^24.9.0"
      jest-util "^24.9.0"
      jest-validate "^24.9.0"
      realpath-native "^1.1.0"
      slash "^2.0.0"
      strip-bom "^3.0.0"
      yargs "^13.3.0"
  
  jest-serializer@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-serializer/download/jest-serializer-24.9.0.tgz#e6d7d7ef96d31e8b9079a714754c5d5c58288e73"
    integrity sha1-5tfX75bTHouQeacUdUxdXFgojnM=
  
  jest-snapshot@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-snapshot/download/jest-snapshot-24.9.0.tgz#ec8e9ca4f2ec0c5c87ae8f925cf97497b0e951ba"
    integrity sha1-7I6cpPLsDFyHro+SXPl0l7DpUbo=
    dependencies:
      "@babel/types" "^7.0.0"
      "@jest/types" "^24.9.0"
      chalk "^2.0.1"
      expect "^24.9.0"
      jest-diff "^24.9.0"
      jest-get-type "^24.9.0"
      jest-matcher-utils "^24.9.0"
      jest-message-util "^24.9.0"
      jest-resolve "^24.9.0"
      mkdirp "^0.5.1"
      natural-compare "^1.4.0"
      pretty-format "^24.9.0"
      semver "^6.2.0"
  
  jest-util@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-util/download/jest-util-24.9.0.tgz#7396814e48536d2e85a37de3e4c431d7cb140162"
    integrity sha1-c5aBTkhTbS6Fo33j5MQx18sUAWI=
    dependencies:
      "@jest/console" "^24.9.0"
      "@jest/fake-timers" "^24.9.0"
      "@jest/source-map" "^24.9.0"
      "@jest/test-result" "^24.9.0"
      "@jest/types" "^24.9.0"
      callsites "^3.0.0"
      chalk "^2.0.1"
      graceful-fs "^4.1.15"
      is-ci "^2.0.0"
      mkdirp "^0.5.1"
      slash "^2.0.0"
      source-map "^0.6.0"
  
  jest-validate@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-validate/download/jest-validate-24.9.0.tgz#0775c55360d173cd854e40180756d4ff52def8ab"
    integrity sha1-B3XFU2DRc82FTkAYB1bU/1Le+Ks=
    dependencies:
      "@jest/types" "^24.9.0"
      camelcase "^5.3.1"
      chalk "^2.0.1"
      jest-get-type "^24.9.0"
      leven "^3.1.0"
      pretty-format "^24.9.0"
  
  jest-watcher@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-watcher/download/jest-watcher-24.9.0.tgz#4b56e5d1ceff005f5b88e528dc9afc8dd4ed2b3b"
    integrity sha1-S1bl0c7/AF9biOUo3Jr8jdTtKzs=
    dependencies:
      "@jest/test-result" "^24.9.0"
      "@jest/types" "^24.9.0"
      "@types/yargs" "^13.0.0"
      ansi-escapes "^3.0.0"
      chalk "^2.0.1"
      jest-util "^24.9.0"
      string-length "^2.0.0"
  
  jest-worker@^24.6.0, jest-worker@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest-worker/download/jest-worker-24.9.0.tgz#5dbfdb5b2d322e98567898238a9697bcce67b3e5"
    integrity sha1-Xb/bWy0yLphWeJgjipaXvM5ns+U=
    dependencies:
      merge-stream "^2.0.0"
      supports-color "^6.1.0"
  
  jest@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/jest/download/jest-24.9.0.tgz#987d290c05a08b52c56188c1002e368edb007171"
    integrity sha1-mH0pDAWgi1LFYYjBAC42jtsAcXE=
    dependencies:
      import-local "^2.0.0"
      jest-cli "^24.9.0"
  
  "js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
    version "4.0.0"
    resolved "http://r.tnpm.oa.com/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
    integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=
  
  js-yaml@^3.13.1:
    version "3.13.1"
    resolved "http://r.tnpm.oa.com/js-yaml/download/js-yaml-3.13.1.tgz#aff151b30bfdfa8e49e05da22e7415e9dfa37847"
    integrity sha1-r/FRswv9+o5J4F2iLnQV6d+jeEc=
    dependencies:
      argparse "^1.0.7"
      esprima "^4.0.0"
  
  jsbn@~0.1.0:
    version "0.1.1"
    resolved "http://r.tnpm.oa.com/jsbn/download/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"
    integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=
  
  jsdom@^11.5.1:
    version "11.12.0"
    resolved "http://r.tnpm.oa.com/jsdom/download/jsdom-11.12.0.tgz#1a80d40ddd378a1de59656e9e6dc5a3ba8657bc8"
    integrity sha1-GoDUDd03ih3lllbp5txaO6hle8g=
    dependencies:
      abab "^2.0.0"
      acorn "^5.5.3"
      acorn-globals "^4.1.0"
      array-equal "^1.0.0"
      cssom ">= 0.3.2 < 0.4.0"
      cssstyle "^1.0.0"
      data-urls "^1.0.0"
      domexception "^1.0.1"
      escodegen "^1.9.1"
      html-encoding-sniffer "^1.0.2"
      left-pad "^1.3.0"
      nwsapi "^2.0.7"
      parse5 "4.0.0"
      pn "^1.1.0"
      request "^2.87.0"
      request-promise-native "^1.0.5"
      sax "^1.2.4"
      symbol-tree "^3.2.2"
      tough-cookie "^2.3.4"
      w3c-hr-time "^1.0.1"
      webidl-conversions "^4.0.2"
      whatwg-encoding "^1.0.3"
      whatwg-mimetype "^2.1.0"
      whatwg-url "^6.4.1"
      ws "^5.2.0"
      xml-name-validator "^3.0.0"
  
  jsesc@^2.5.1:
    version "2.5.2"
    resolved "http://r.tnpm.oa.com/jsesc/download/jsesc-2.5.2.tgz#80564d2e483dacf6e8ef209650a67df3f0c283a4"
    integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=
  
  json-parse-better-errors@^1.0.1:
    version "1.0.2"
    resolved "http://r.tnpm.oa.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"
    integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=
  
  json-schema-traverse@^0.4.1:
    version "0.4.1"
    resolved "http://r.tnpm.oa.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
    integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=
  
  json-schema@0.2.3:
    version "0.2.3"
    resolved "http://r.tnpm.oa.com/json-schema/download/json-schema-0.2.3.tgz#b480c892e59a2f05954ce727bd3f2a4e882f9e13"
    integrity sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=
  
  json-stable-stringify-without-jsonify@^1.0.1:
    version "1.0.1"
    resolved "http://r.tnpm.oa.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
    integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=
  
  json-stringify-safe@~5.0.1:
    version "5.0.1"
    resolved "http://r.tnpm.oa.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"
    integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=
  
  json5@2.x, json5@^2.1.0:
    version "2.1.1"
    resolved "http://r.tnpm.oa.com/json5/download/json5-2.1.1.tgz#81b6cb04e9ba496f1c7005d07b4368a2638f90b6"
    integrity sha1-gbbLBOm6SW8ccAXQe0NoomOPkLY=
    dependencies:
      minimist "^1.2.0"
  
  jsprim@^1.2.2:
    version "1.4.1"
    resolved "http://r.tnpm.oa.com/jsprim/download/jsprim-1.4.1.tgz#313e66bc1e5cc06e438bc1b7499c2e5c56acb6a2"
    integrity sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=
    dependencies:
      assert-plus "1.0.0"
      extsprintf "1.3.0"
      json-schema "0.2.3"
      verror "1.10.0"
  
  kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
    version "3.2.2"
    resolved "http://r.tnpm.oa.com/kind-of/download/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
    integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
    dependencies:
      is-buffer "^1.1.5"
  
  kind-of@^4.0.0:
    version "4.0.0"
    resolved "http://r.tnpm.oa.com/kind-of/download/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
    integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
    dependencies:
      is-buffer "^1.1.5"
  
  kind-of@^5.0.0:
    version "5.1.0"
    resolved "http://r.tnpm.oa.com/kind-of/download/kind-of-5.1.0.tgz#729c91e2d857b7a419a1f9aa65685c4c33f5845d"
    integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=
  
  kind-of@^6.0.0, kind-of@^6.0.2:
    version "6.0.2"
    resolved "http://r.tnpm.oa.com/kind-of/download/kind-of-6.0.2.tgz#01146b36a6218e64e58f3a8d66de5d7fc6f6d051"
    integrity sha1-ARRrNqYhjmTljzqNZt5df8b20FE=
  
  kleur@^3.0.3:
    version "3.0.3"
    resolved "http://r.tnpm.oa.com/kleur/download/kleur-3.0.3.tgz#a79c9ecc86ee1ce3fa6206d1216c501f147fc07e"
    integrity sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=
  
  left-pad@^1.3.0:
    version "1.3.0"
    resolved "http://r.tnpm.oa.com/left-pad/download/left-pad-1.3.0.tgz#5b8a3a7765dfe001261dde915589e782f8c94d1e"
    integrity sha1-W4o6d2Xf4AEmHd6RVYnngvjJTR4=
  
  leven@^3.1.0:
    version "3.1.0"
    resolved "http://r.tnpm.oa.com/leven/download/leven-3.1.0.tgz#77891de834064cccba82ae7842bb6b14a13ed7f2"
    integrity sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=
  
  levn@^0.3.0, levn@~0.3.0:
    version "0.3.0"
    resolved "http://r.tnpm.oa.com/levn/download/levn-0.3.0.tgz#3b09924edf9f083c0490fdd4c0bc4421e04764ee"
    integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
    dependencies:
      prelude-ls "~1.1.2"
      type-check "~0.3.2"
  
  lines-and-columns@^1.1.6:
    version "1.1.6"
    resolved "http://r.tnpm.oa.com/lines-and-columns/download/lines-and-columns-1.1.6.tgz#1c00c743b433cd0a4e80758f7b64a57440d9ff00"
    integrity sha1-HADHQ7QzzQpOgHWPe2SldEDZ/wA=
  
  lint-staged@^9.5.0:
    version "9.5.0"
    resolved "http://r.tnpm.oa.com/lint-staged/download/lint-staged-9.5.0.tgz#290ec605252af646d9b74d73a0fa118362b05a33"
    integrity sha1-KQ7GBSUq9kbZt01zoPoRg2KwWjM=
    dependencies:
      chalk "^2.4.2"
      commander "^2.20.0"
      cosmiconfig "^5.2.1"
      debug "^4.1.1"
      dedent "^0.7.0"
      del "^5.0.0"
      execa "^2.0.3"
      listr "^0.14.3"
      log-symbols "^3.0.0"
      micromatch "^4.0.2"
      normalize-path "^3.0.0"
      please-upgrade-node "^3.1.1"
      string-argv "^0.3.0"
      stringify-object "^3.3.0"
  
  listr-silent-renderer@^1.1.1:
    version "1.1.1"
    resolved "http://r.tnpm.oa.com/listr-silent-renderer/download/listr-silent-renderer-1.1.1.tgz#924b5a3757153770bf1a8e3fbf74b8bbf3f9242e"
    integrity sha1-kktaN1cVN3C/Go4/v3S4u/P5JC4=
  
  listr-update-renderer@^0.5.0:
    version "0.5.0"
    resolved "http://r.tnpm.oa.com/listr-update-renderer/download/listr-update-renderer-0.5.0.tgz#4ea8368548a7b8aecb7e06d8c95cb45ae2ede6a2"
    integrity sha1-Tqg2hUinuK7LfgbYyVy0WuLt5qI=
    dependencies:
      chalk "^1.1.3"
      cli-truncate "^0.2.1"
      elegant-spinner "^1.0.1"
      figures "^1.7.0"
      indent-string "^3.0.0"
      log-symbols "^1.0.2"
      log-update "^2.3.0"
      strip-ansi "^3.0.1"
  
  listr-verbose-renderer@^0.5.0:
    version "0.5.0"
    resolved "http://r.tnpm.oa.com/listr-verbose-renderer/download/listr-verbose-renderer-0.5.0.tgz#f1132167535ea4c1261102b9f28dac7cba1e03db"
    integrity sha1-8RMhZ1NepMEmEQK58o2sfLoeA9s=
    dependencies:
      chalk "^2.4.1"
      cli-cursor "^2.1.0"
      date-fns "^1.27.2"
      figures "^2.0.0"
  
  listr@^0.14.3:
    version "0.14.3"
    resolved "http://r.tnpm.oa.com/listr/download/listr-0.14.3.tgz#2fea909604e434be464c50bddba0d496928fa586"
    integrity sha1-L+qQlgTkNL5GTFC926DUlpKPpYY=
    dependencies:
      "@samverschueren/stream-to-observable" "^0.3.0"
      is-observable "^1.1.0"
      is-promise "^2.1.0"
      is-stream "^1.1.0"
      listr-silent-renderer "^1.1.1"
      listr-update-renderer "^0.5.0"
      listr-verbose-renderer "^0.5.0"
      p-map "^2.0.0"
      rxjs "^6.3.3"
  
  load-json-file@^4.0.0:
    version "4.0.0"
    resolved "http://r.tnpm.oa.com/load-json-file/download/load-json-file-4.0.0.tgz#2f5f45ab91e33216234fd53adab668eb4ec0993b"
    integrity sha1-L19Fq5HjMhYjT9U62rZo607AmTs=
    dependencies:
      graceful-fs "^4.1.2"
      parse-json "^4.0.0"
      pify "^3.0.0"
      strip-bom "^3.0.0"
  
  locate-path@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/locate-path/download/locate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e"
    integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
    dependencies:
      p-locate "^3.0.0"
      path-exists "^3.0.0"
  
  locate-path@^5.0.0:
    version "5.0.0"
    resolved "http://r.tnpm.oa.com/locate-path/download/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
    integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
    dependencies:
      p-locate "^4.1.0"
  
  lodash.memoize@4.x:
    version "4.1.2"
    resolved "http://r.tnpm.oa.com/lodash.memoize/download/lodash.memoize-4.1.2.tgz#bcc6c49a42a2840ed997f323eada5ecd182e0bfe"
    integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=
  
  lodash.sortby@^4.7.0:
    version "4.7.0"
    resolved "http://r.tnpm.oa.com/lodash.sortby/download/lodash.sortby-4.7.0.tgz#edd14c824e2cc9c1e0b0a1b42bb5210516a42438"
    integrity sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=
  
  lodash.unescape@4.0.1:
    version "4.0.1"
    resolved "http://r.tnpm.oa.com/lodash.unescape/download/lodash.unescape-4.0.1.tgz#bf2249886ce514cda112fae9218cdc065211fc9c"
    integrity sha1-vyJJiGzlFM2hEvrpIYzcBlIR/Jw=
  
  lodash@^4.17.13, lodash@^4.17.14, lodash@^4.17.15:
    version "4.17.15"
    resolved "http://r.tnpm.oa.com/lodash/download/lodash-4.17.15.tgz#b447f6670a0455bbfeedd11392eff330ea097548"
    integrity sha1-tEf2ZwoEVbv+7dETku/zMOoJdUg=
  
  log-symbols@^1.0.2:
    version "1.0.2"
    resolved "http://r.tnpm.oa.com/log-symbols/download/log-symbols-1.0.2.tgz#376ff7b58ea3086a0f09facc74617eca501e1a18"
    integrity sha1-N2/3tY6jCGoPCfrMdGF+ylAeGhg=
    dependencies:
      chalk "^1.0.0"
  
  log-symbols@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/log-symbols/download/log-symbols-3.0.0.tgz#f3a08516a5dea893336a7dee14d18a1cfdab77c4"
    integrity sha1-86CFFqXeqJMzan3uFNGKHP2rd8Q=
    dependencies:
      chalk "^2.4.2"
  
  log-update@^2.3.0:
    version "2.3.0"
    resolved "http://r.tnpm.oa.com/log-update/download/log-update-2.3.0.tgz#88328fd7d1ce7938b29283746f0b1bc126b24708"
    integrity sha1-iDKP19HOeTiykoN0bwsbwSayRwg=
    dependencies:
      ansi-escapes "^3.0.0"
      cli-cursor "^2.0.0"
      wrap-ansi "^3.0.1"
  
  loose-envify@^1.0.0:
    version "1.4.0"
    resolved "http://r.tnpm.oa.com/loose-envify/download/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
    integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
    dependencies:
      js-tokens "^3.0.0 || ^4.0.0"
  
  make-dir@^2.1.0:
    version "2.1.0"
    resolved "http://r.tnpm.oa.com/make-dir/download/make-dir-2.1.0.tgz#5f0310e18b8be898cc07009295a30ae41e91e6f5"
    integrity sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=
    dependencies:
      pify "^4.0.1"
      semver "^5.6.0"
  
  make-error@1.x:
    version "1.3.5"
    resolved "http://r.tnpm.oa.com/make-error/download/make-error-1.3.5.tgz#efe4e81f6db28cadd605c70f29c831b58ef776c8"
    integrity sha1-7+ToH22yjK3WBccPKcgxtY73dsg=
  
  makeerror@1.0.x:
    version "1.0.11"
    resolved "http://r.tnpm.oa.com/makeerror/download/makeerror-1.0.11.tgz#e01a5c9109f2af79660e4e8b9587790184f5a96c"
    integrity sha1-4BpckQnyr3lmDk6LlYd5AYT1qWw=
    dependencies:
      tmpl "1.0.x"
  
  map-cache@^0.2.2:
    version "0.2.2"
    resolved "http://r.tnpm.oa.com/map-cache/download/map-cache-0.2.2.tgz#c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf"
    integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=
  
  map-visit@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/map-visit/download/map-visit-1.0.0.tgz#ecdca8f13144e660f1b5bd41f12f3479d98dfb8f"
    integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
    dependencies:
      object-visit "^1.0.0"
  
  merge-stream@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/merge-stream/download/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
    integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=
  
  merge2@^1.2.3, merge2@^1.3.0:
    version "1.3.0"
    resolved "http://r.tnpm.oa.com/merge2/download/merge2-1.3.0.tgz#5b366ee83b2f1582c48f87e47cf1a9352103ca81"
    integrity sha1-WzZu6DsvFYLEj4fkfPGpNSEDyoE=
  
  micromatch@^3.1.10, micromatch@^3.1.4:
    version "3.1.10"
    resolved "http://r.tnpm.oa.com/micromatch/download/micromatch-3.1.10.tgz#70859bc95c9840952f359a068a3fc49f9ecfac23"
    integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
    dependencies:
      arr-diff "^4.0.0"
      array-unique "^0.3.2"
      braces "^2.3.1"
      define-property "^2.0.2"
      extend-shallow "^3.0.2"
      extglob "^2.0.4"
      fragment-cache "^0.2.1"
      kind-of "^6.0.2"
      nanomatch "^1.2.9"
      object.pick "^1.3.0"
      regex-not "^1.0.0"
      snapdragon "^0.8.1"
      to-regex "^3.0.2"
  
  micromatch@^4.0.2:
    version "4.0.2"
    resolved "http://r.tnpm.oa.com/micromatch/download/micromatch-4.0.2.tgz#4fcb0999bf9fbc2fcbdd212f6d629b9a56c39259"
    integrity sha1-T8sJmb+fvC/L3SEvbWKbmlbDklk=
    dependencies:
      braces "^3.0.1"
      picomatch "^2.0.5"
  
  mime-db@1.42.0:
    version "1.42.0"
    resolved "http://r.tnpm.oa.com/mime-db/download/mime-db-1.42.0.tgz#3e252907b4c7adb906597b4b65636272cf9e7bac"
    integrity sha1-PiUpB7THrbkGWXtLZWNics+ee6w=
  
  mime-types@^2.1.12, mime-types@~2.1.19:
    version "2.1.25"
    resolved "http://r.tnpm.oa.com/mime-types/download/mime-types-2.1.25.tgz#39772d46621f93e2a80a856c53b86a62156a6437"
    integrity sha1-OXctRmIfk+KoCoVsU7hqYhVqZDc=
    dependencies:
      mime-db "1.42.0"
  
  mimic-fn@^1.0.0:
    version "1.2.0"
    resolved "http://r.tnpm.oa.com/mimic-fn/download/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"
    integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=
  
  mimic-fn@^2.1.0:
    version "2.1.0"
    resolved "http://r.tnpm.oa.com/mimic-fn/download/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
    integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=
  
  minimatch@^3.0.4:
    version "3.0.4"
    resolved "http://r.tnpm.oa.com/minimatch/download/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
    integrity sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=
    dependencies:
      brace-expansion "^1.1.7"
  
  minimist@0.0.8:
    version "0.0.8"
    resolved "http://r.tnpm.oa.com/minimist/download/minimist-0.0.8.tgz#857fcabfc3397d2625b8228262e86aa7a011b05d"
    integrity sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=
  
  minimist@^1.1.1, minimist@^1.2.0:
    version "1.2.0"
    resolved "http://r.tnpm.oa.com/minimist/download/minimist-1.2.0.tgz#a35008b20f41383eec1fb914f4cd5df79a264284"
    integrity sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ=
  
  minimist@~0.0.1:
    version "0.0.10"
    resolved "http://r.tnpm.oa.com/minimist/download/minimist-0.0.10.tgz#de3f98543dbf96082be48ad1a0c7cda836301dcf"
    integrity sha1-3j+YVD2/lggr5IrRoMfNqDYwHc8=
  
  minipass@^2.6.0, minipass@^2.8.6, minipass@^2.9.0:
    version "2.9.0"
    resolved "http://r.tnpm.oa.com/minipass/download/minipass-2.9.0.tgz#e713762e7d3e32fed803115cf93e04bca9fcc9a6"
    integrity sha1-5xN2Ln0+Mv7YAxFc+T4EvKn8yaY=
    dependencies:
      safe-buffer "^5.1.2"
      yallist "^3.0.0"
  
  minizlib@^1.2.1:
    version "1.3.3"
    resolved "http://r.tnpm.oa.com/minizlib/download/minizlib-1.3.3.tgz#2290de96818a34c29551c8a8d301216bd65a861d"
    integrity sha1-IpDeloGKNMKVUcio0wEha9Zahh0=
    dependencies:
      minipass "^2.9.0"
  
  mixin-deep@^1.2.0:
    version "1.3.2"
    resolved "http://r.tnpm.oa.com/mixin-deep/download/mixin-deep-1.3.2.tgz#1120b43dc359a785dce65b55b82e257ccf479566"
    integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
    dependencies:
      for-in "^1.0.2"
      is-extendable "^1.0.1"
  
  mkdirp@0.x, mkdirp@^0.5.0, mkdirp@^0.5.1:
    version "0.5.1"
    resolved "http://r.tnpm.oa.com/mkdirp/download/mkdirp-0.5.1.tgz#30057438eac6cf7f8c4767f38648d6697d75c903"
    integrity sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=
    dependencies:
      minimist "0.0.8"
  
  ms@2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
    integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=
  
  ms@^2.1.1:
    version "2.1.2"
    resolved "http://r.tnpm.oa.com/ms/download/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
    integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=
  
  mute-stream@0.0.8:
    version "0.0.8"
    resolved "http://r.tnpm.oa.com/mute-stream/download/mute-stream-0.0.8.tgz#1630c42b2251ff81e2a283de96a5497ea92e5e0d"
    integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=
  
  nan@^2.12.1:
    version "2.14.0"
    resolved "http://r.tnpm.oa.com/nan/download/nan-2.14.0.tgz#7818f722027b2459a86f0295d434d1fc2336c52c"
    integrity sha1-eBj3IgJ7JFmobwKV1DTR/CM2xSw=
  
  nanomatch@^1.2.9:
    version "1.2.13"
    resolved "http://r.tnpm.oa.com/nanomatch/download/nanomatch-1.2.13.tgz#b87a8aa4fc0de8fe6be88895b38983ff265bd119"
    integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
    dependencies:
      arr-diff "^4.0.0"
      array-unique "^0.3.2"
      define-property "^2.0.2"
      extend-shallow "^3.0.2"
      fragment-cache "^0.2.1"
      is-windows "^1.0.2"
      kind-of "^6.0.2"
      object.pick "^1.3.0"
      regex-not "^1.0.0"
      snapdragon "^0.8.1"
      to-regex "^3.0.1"
  
  natural-compare@^1.4.0:
    version "1.4.0"
    resolved "http://r.tnpm.oa.com/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
    integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=
  
  needle@^2.2.1:
    version "2.4.0"
    resolved "http://r.tnpm.oa.com/needle/download/needle-2.4.0.tgz#6833e74975c444642590e15a750288c5f939b57c"
    integrity sha1-aDPnSXXERGQlkOFadQKIxfk5tXw=
    dependencies:
      debug "^3.2.6"
      iconv-lite "^0.4.4"
      sax "^1.2.4"
  
  neo-async@^2.6.0:
    version "2.6.1"
    resolved "http://r.tnpm.oa.com/neo-async/download/neo-async-2.6.1.tgz#ac27ada66167fa8849a6addd837f6b189ad2081c"
    integrity sha1-rCetpmFn+ohJpq3dg39rGJrSCBw=
  
  nice-try@^1.0.4:
    version "1.0.5"
    resolved "http://r.tnpm.oa.com/nice-try/download/nice-try-1.0.5.tgz#a3378a7696ce7d223e88fc9b764bd7ef1089e366"
    integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=
  
  node-int64@^0.4.0:
    version "0.4.0"
    resolved "http://r.tnpm.oa.com/node-int64/download/node-int64-0.4.0.tgz#87a9065cdb355d3182d8f94ce11188b825c68a3b"
    integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=
  
  node-modules-regexp@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/node-modules-regexp/download/node-modules-regexp-1.0.0.tgz#8d9dbe28964a4ac5712e9131642107c71e90ec40"
    integrity sha1-jZ2+KJZKSsVxLpExZCEHxx6Q7EA=
  
  node-notifier@^5.4.2:
    version "5.4.3"
    resolved "http://r.tnpm.oa.com/node-notifier/download/node-notifier-5.4.3.tgz#cb72daf94c93904098e28b9c590fd866e464bd50"
    integrity sha1-y3La+UyTkECY4oucWQ/YZuRkvVA=
    dependencies:
      growly "^1.3.0"
      is-wsl "^1.1.0"
      semver "^5.5.0"
      shellwords "^0.1.1"
      which "^1.3.0"
  
  node-pre-gyp@^0.12.0:
    version "0.12.0"
    resolved "http://r.tnpm.oa.com/node-pre-gyp/download/node-pre-gyp-0.12.0.tgz#39ba4bb1439da030295f899e3b520b7785766149"
    integrity sha1-ObpLsUOdoDApX4meO1ILd4V2YUk=
    dependencies:
      detect-libc "^1.0.2"
      mkdirp "^0.5.1"
      needle "^2.2.1"
      nopt "^4.0.1"
      npm-packlist "^1.1.6"
      npmlog "^4.0.2"
      rc "^1.2.7"
      rimraf "^2.6.1"
      semver "^5.3.0"
      tar "^4"
  
  nopt@^4.0.1:
    version "4.0.1"
    resolved "http://r.tnpm.oa.com/nopt/download/nopt-4.0.1.tgz#d0d4685afd5415193c8c7505602d0d17cd64474d"
    integrity sha1-0NRoWv1UFRk8jHUFYC0NF81kR00=
    dependencies:
      abbrev "1"
      osenv "^0.1.4"
  
  normalize-package-data@^2.3.2, normalize-package-data@^2.5.0:
    version "2.5.0"
    resolved "http://r.tnpm.oa.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
    integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
    dependencies:
      hosted-git-info "^2.1.4"
      resolve "^1.10.0"
      semver "2 || 3 || 4 || 5"
      validate-npm-package-license "^3.0.1"
  
  normalize-path@^2.1.1:
    version "2.1.1"
    resolved "http://r.tnpm.oa.com/normalize-path/download/normalize-path-2.1.1.tgz#1ab28b556e198363a8c1a6f7e6fa20137fe6aed9"
    integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
    dependencies:
      remove-trailing-separator "^1.0.1"
  
  normalize-path@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/normalize-path/download/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
    integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=
  
  npm-bundled@^1.0.1:
    version "1.0.6"
    resolved "http://r.tnpm.oa.com/npm-bundled/download/npm-bundled-1.0.6.tgz#e7ba9aadcef962bb61248f91721cd932b3fe6bdd"
    integrity sha1-57qarc75YrthJI+RchzZMrP+a90=
  
  npm-packlist@^1.1.6:
    version "1.4.6"
    resolved "http://r.tnpm.oa.com/npm-packlist/download/npm-packlist-1.4.6.tgz#53ba3ed11f8523079f1457376dd379ee4ea42ff4"
    integrity sha1-U7o+0R+FIwefFFc3bdN57k6kL/Q=
    dependencies:
      ignore-walk "^3.0.1"
      npm-bundled "^1.0.1"
  
  npm-run-path@^2.0.0:
    version "2.0.2"
    resolved "http://r.tnpm.oa.com/npm-run-path/download/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
    integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
    dependencies:
      path-key "^2.0.0"
  
  npm-run-path@^3.0.0:
    version "3.1.0"
    resolved "http://r.tnpm.oa.com/npm-run-path/download/npm-run-path-3.1.0.tgz#7f91be317f6a466efed3c9f2980ad8a4ee8b0fa5"
    integrity sha1-f5G+MX9qRm7+08nymArYpO6LD6U=
    dependencies:
      path-key "^3.0.0"
  
  npmlog@^4.0.2:
    version "4.1.2"
    resolved "http://r.tnpm.oa.com/npmlog/download/npmlog-4.1.2.tgz#08a7f2a8bf734604779a9efa4ad5cc717abb954b"
    integrity sha1-CKfyqL9zRgR3mp76StXMcXq7lUs=
    dependencies:
      are-we-there-yet "~1.1.2"
      console-control-strings "~1.1.0"
      gauge "~2.7.3"
      set-blocking "~2.0.0"
  
  number-is-nan@^1.0.0:
    version "1.0.1"
    resolved "http://r.tnpm.oa.com/number-is-nan/download/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"
    integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=
  
  nwsapi@^2.0.7:
    version "2.2.0"
    resolved "http://r.tnpm.oa.com/nwsapi/download/nwsapi-2.2.0.tgz#204879a9e3d068ff2a55139c2c772780681a38b7"
    integrity sha1-IEh5qePQaP8qVROcLHcngGgaOLc=
  
  oauth-sign@~0.9.0:
    version "0.9.0"
    resolved "http://r.tnpm.oa.com/oauth-sign/download/oauth-sign-0.9.0.tgz#47a7b016baa68b5fa0ecf3dee08a85c679ac6455"
    integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=
  
  object-assign@^4.1.0:
    version "4.1.1"
    resolved "http://r.tnpm.oa.com/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
    integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=
  
  object-copy@^0.1.0:
    version "0.1.0"
    resolved "http://r.tnpm.oa.com/object-copy/download/object-copy-0.1.0.tgz#7e7d858b781bd7c991a41ba975ed3812754e998c"
    integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
    dependencies:
      copy-descriptor "^0.1.0"
      define-property "^0.2.5"
      kind-of "^3.0.3"
  
  object-inspect@^1.7.0:
    version "1.7.0"
    resolved "http://r.tnpm.oa.com/object-inspect/download/object-inspect-1.7.0.tgz#f4f6bd181ad77f006b5ece60bd0b6f398ff74a67"
    integrity sha1-9Pa9GBrXfwBrXs5gvQtvOY/3Smc=
  
  object-keys@^1.0.12, object-keys@^1.1.1:
    version "1.1.1"
    resolved "http://r.tnpm.oa.com/object-keys/download/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
    integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=
  
  object-visit@^1.0.0:
    version "1.0.1"
    resolved "http://r.tnpm.oa.com/object-visit/download/object-visit-1.0.1.tgz#f79c4493af0c5377b59fe39d395e41042dd045bb"
    integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
    dependencies:
      isobject "^3.0.0"
  
  object.getownpropertydescriptors@^2.0.3:
    version "2.0.3"
    resolved "http://r.tnpm.oa.com/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.0.3.tgz#8758c846f5b407adab0f236e0986f14b051caa16"
    integrity sha1-h1jIRvW0B62rDyNuCYbxSwUcqhY=
    dependencies:
      define-properties "^1.1.2"
      es-abstract "^1.5.1"
  
  object.pick@^1.3.0:
    version "1.3.0"
    resolved "http://r.tnpm.oa.com/object.pick/download/object.pick-1.3.0.tgz#87a10ac4c1694bd2e1cbf53591a66141fb5dd747"
    integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
    dependencies:
      isobject "^3.0.1"
  
  once@^1.3.0, once@^1.3.1, once@^1.4.0:
    version "1.4.0"
    resolved "http://r.tnpm.oa.com/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
    integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
    dependencies:
      wrappy "1"
  
  onetime@^2.0.0:
    version "2.0.1"
    resolved "http://r.tnpm.oa.com/onetime/download/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
    integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
    dependencies:
      mimic-fn "^1.0.0"
  
  onetime@^5.1.0:
    version "5.1.0"
    resolved "http://r.tnpm.oa.com/onetime/download/onetime-5.1.0.tgz#fff0f3c91617fe62bb50189636e99ac8a6df7be5"
    integrity sha1-//DzyRYX/mK7UBiWNumayKbfe+U=
    dependencies:
      mimic-fn "^2.1.0"
  
  opencollective-postinstall@^2.0.2:
    version "2.0.2"
    resolved "http://r.tnpm.oa.com/opencollective-postinstall/download/opencollective-postinstall-2.0.2.tgz#5657f1bede69b6e33a45939b061eb53d3c6c3a89"
    integrity sha1-Vlfxvt5ptuM6RZObBh61PTxsOok=
  
  optimist@^0.6.1:
    version "0.6.1"
    resolved "http://r.tnpm.oa.com/optimist/download/optimist-0.6.1.tgz#da3ea74686fa21a19a111c326e90eb15a0196686"
    integrity sha1-2j6nRob6IaGaERwybpDrFaAZZoY=
    dependencies:
      minimist "~0.0.1"
      wordwrap "~0.0.2"
  
  optionator@^0.8.1, optionator@^0.8.3:
    version "0.8.3"
    resolved "http://r.tnpm.oa.com/optionator/download/optionator-0.8.3.tgz#84fa1d036fe9d3c7e21d99884b601167ec8fb495"
    integrity sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=
    dependencies:
      deep-is "~0.1.3"
      fast-levenshtein "~2.0.6"
      levn "~0.3.0"
      prelude-ls "~1.1.2"
      type-check "~0.3.2"
      word-wrap "~1.2.3"
  
  os-homedir@^1.0.0:
    version "1.0.2"
    resolved "http://r.tnpm.oa.com/os-homedir/download/os-homedir-1.0.2.tgz#ffbc4988336e0e833de0c168c7ef152121aa7fb3"
    integrity sha1-/7xJiDNuDoM94MFox+8VISGqf7M=
  
  os-tmpdir@^1.0.0, os-tmpdir@~1.0.2:
    version "1.0.2"
    resolved "http://r.tnpm.oa.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
    integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=
  
  osenv@^0.1.4:
    version "0.1.5"
    resolved "http://r.tnpm.oa.com/osenv/download/osenv-0.1.5.tgz#85cdfafaeb28e8677f416e287592b5f3f49ea410"
    integrity sha1-hc36+uso6Gd/QW4odZK18/SepBA=
    dependencies:
      os-homedir "^1.0.0"
      os-tmpdir "^1.0.0"
  
  p-each-series@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/p-each-series/download/p-each-series-1.0.0.tgz#930f3d12dd1f50e7434457a22cd6f04ac6ad7f71"
    integrity sha1-kw89Et0fUOdDRFeiLNbwSsatf3E=
    dependencies:
      p-reduce "^1.0.0"
  
  p-finally@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/p-finally/download/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"
    integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=
  
  p-finally@^2.0.0:
    version "2.0.1"
    resolved "http://r.tnpm.oa.com/p-finally/download/p-finally-2.0.1.tgz#bd6fcaa9c559a096b680806f4d657b3f0f240561"
    integrity sha1-vW/KqcVZoJa2gIBvTWV7Pw8kBWE=
  
  p-limit@^2.0.0, p-limit@^2.2.0:
    version "2.2.1"
    resolved "http://r.tnpm.oa.com/p-limit/download/p-limit-2.2.1.tgz#aa07a788cc3151c939b5131f63570f0dd2009537"
    integrity sha1-qgeniMwxUck5tRMfY1cPDdIAlTc=
    dependencies:
      p-try "^2.0.0"
  
  p-locate@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/p-locate/download/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4"
    integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
    dependencies:
      p-limit "^2.0.0"
  
  p-locate@^4.1.0:
    version "4.1.0"
    resolved "http://r.tnpm.oa.com/p-locate/download/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
    integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
    dependencies:
      p-limit "^2.2.0"
  
  p-map@^2.0.0:
    version "2.1.0"
    resolved "http://r.tnpm.oa.com/p-map/download/p-map-2.1.0.tgz#310928feef9c9ecc65b68b17693018a665cea175"
    integrity sha1-MQko/u+cnsxltosXaTAYpmXOoXU=
  
  p-map@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/p-map/download/p-map-3.0.0.tgz#d704d9af8a2ba684e2600d9a215983d4141a979d"
    integrity sha1-1wTZr4orpoTiYA2aIVmD1BQal50=
    dependencies:
      aggregate-error "^3.0.0"
  
  p-reduce@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/p-reduce/download/p-reduce-1.0.0.tgz#18c2b0dd936a4690a529f8231f58a0fdb6a47dfa"
    integrity sha1-GMKw3ZNqRpClKfgjH1ig/bakffo=
  
  p-try@^2.0.0:
    version "2.2.0"
    resolved "http://r.tnpm.oa.com/p-try/download/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
    integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=
  
  parent-module@^1.0.0:
    version "1.0.1"
    resolved "http://r.tnpm.oa.com/parent-module/download/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
    integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
    dependencies:
      callsites "^3.0.0"
  
  parse-json@^4.0.0:
    version "4.0.0"
    resolved "http://r.tnpm.oa.com/parse-json/download/parse-json-4.0.0.tgz#be35f5425be1f7f6c747184f98a788cb99477ee0"
    integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
    dependencies:
      error-ex "^1.3.1"
      json-parse-better-errors "^1.0.1"
  
  parse-json@^5.0.0:
    version "5.0.0"
    resolved "http://r.tnpm.oa.com/parse-json/download/parse-json-5.0.0.tgz#73e5114c986d143efa3712d4ea24db9a4266f60f"
    integrity sha1-c+URTJhtFD76NxLU6iTbmkJm9g8=
    dependencies:
      "@babel/code-frame" "^7.0.0"
      error-ex "^1.3.1"
      json-parse-better-errors "^1.0.1"
      lines-and-columns "^1.1.6"
  
  parse5@4.0.0:
    version "4.0.0"
    resolved "http://r.tnpm.oa.com/parse5/download/parse5-4.0.0.tgz#6d78656e3da8d78b4ec0b906f7c08ef1dfe3f608"
    integrity sha1-bXhlbj2o14tOwLkG98CO8d/j9gg=
  
  pascalcase@^0.1.1:
    version "0.1.1"
    resolved "http://r.tnpm.oa.com/pascalcase/download/pascalcase-0.1.1.tgz#b363e55e8006ca6fe21784d2db22bd15d7917f14"
    integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=
  
  path-exists@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/path-exists/download/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"
    integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=
  
  path-exists@^4.0.0:
    version "4.0.0"
    resolved "http://r.tnpm.oa.com/path-exists/download/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
    integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=
  
  path-is-absolute@^1.0.0:
    version "1.0.1"
    resolved "http://r.tnpm.oa.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
    integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=
  
  path-key@^2.0.0, path-key@^2.0.1:
    version "2.0.1"
    resolved "http://r.tnpm.oa.com/path-key/download/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"
    integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=
  
  path-key@^3.0.0, path-key@^3.1.0:
    version "3.1.1"
    resolved "http://r.tnpm.oa.com/path-key/download/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
    integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=
  
  path-parse@^1.0.6:
    version "1.0.6"
    resolved "http://r.tnpm.oa.com/path-parse/download/path-parse-1.0.6.tgz#d62dbb5679405d72c4737ec58600e9ddcf06d24c"
    integrity sha1-1i27VnlAXXLEc37FhgDp3c8G0kw=
  
  path-type@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/path-type/download/path-type-3.0.0.tgz#cef31dc8e0a1a3bb0d105c0cd97cf3bf47f4e36f"
    integrity sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=
    dependencies:
      pify "^3.0.0"
  
  path-type@^4.0.0:
    version "4.0.0"
    resolved "http://r.tnpm.oa.com/path-type/download/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
    integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=
  
  performance-now@^2.1.0:
    version "2.1.0"
    resolved "http://r.tnpm.oa.com/performance-now/download/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
    integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=
  
  picomatch@^2.0.5:
    version "2.1.1"
    resolved "http://r.tnpm.oa.com/picomatch/download/picomatch-2.1.1.tgz#ecdfbea7704adb5fe6fb47f9866c4c0e15e905c5"
    integrity sha1-7N++p3BK21/m+0f5hmxMDhXpBcU=
  
  pify@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/pify/download/pify-3.0.0.tgz#e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176"
    integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=
  
  pify@^4.0.1:
    version "4.0.1"
    resolved "http://r.tnpm.oa.com/pify/download/pify-4.0.1.tgz#4b2cd25c50d598735c50292224fd8c6df41e3231"
    integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=
  
  pirates@^4.0.1:
    version "4.0.1"
    resolved "http://r.tnpm.oa.com/pirates/download/pirates-4.0.1.tgz#643a92caf894566f91b2b986d2c66950a8e2fb87"
    integrity sha1-ZDqSyviUVm+RsrmG0sZpUKji+4c=
    dependencies:
      node-modules-regexp "^1.0.0"
  
  pkg-dir@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/pkg-dir/download/pkg-dir-3.0.0.tgz#2749020f239ed990881b1f71210d51eb6523bea3"
    integrity sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=
    dependencies:
      find-up "^3.0.0"
  
  pkg-dir@^4.2.0:
    version "4.2.0"
    resolved "http://r.tnpm.oa.com/pkg-dir/download/pkg-dir-4.2.0.tgz#f099133df7ede422e81d1d8448270eeb3e4261f3"
    integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
    dependencies:
      find-up "^4.0.0"
  
  please-upgrade-node@^3.1.1, please-upgrade-node@^3.2.0:
    version "3.2.0"
    resolved "http://r.tnpm.oa.com/please-upgrade-node/download/please-upgrade-node-3.2.0.tgz#aeddd3f994c933e4ad98b99d9a556efa0e2fe942"
    integrity sha1-rt3T+ZTJM+StmLmdmlVu+g4v6UI=
    dependencies:
      semver-compare "^1.0.0"
  
  pn@^1.1.0:
    version "1.1.0"
    resolved "http://r.tnpm.oa.com/pn/download/pn-1.1.0.tgz#e2f4cef0e219f463c179ab37463e4e1ecdccbafb"
    integrity sha1-4vTO8OIZ9GPBeas3Rj5OHs3Muvs=
  
  posix-character-classes@^0.1.0:
    version "0.1.1"
    resolved "http://r.tnpm.oa.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz#01eac0fe3b5af71a2a6c02feabb8c1fef7e00eab"
    integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=
  
  prelude-ls@~1.1.2:
    version "1.1.2"
    resolved "http://r.tnpm.oa.com/prelude-ls/download/prelude-ls-1.1.2.tgz#21932a549f5e52ffd9a827f570e04be62a97da54"
    integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=
  
  pretty-format@^24.9.0:
    version "24.9.0"
    resolved "http://r.tnpm.oa.com/pretty-format/download/pretty-format-24.9.0.tgz#12fac31b37019a4eea3c11aa9a959eb7628aa7c9"
    integrity sha1-EvrDGzcBmk7qPBGqmpWet2KKp8k=
    dependencies:
      "@jest/types" "^24.9.0"
      ansi-regex "^4.0.0"
      ansi-styles "^3.2.0"
      react-is "^16.8.4"
  
  process-nextick-args@~2.0.0:
    version "2.0.1"
    resolved "http://r.tnpm.oa.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
    integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=
  
  progress@^2.0.0:
    version "2.0.3"
    resolved "http://r.tnpm.oa.com/progress/download/progress-2.0.3.tgz#7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8"
    integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=
  
  prompts@^2.0.1:
    version "2.3.0"
    resolved "http://r.tnpm.oa.com/prompts/download/prompts-2.3.0.tgz#a444e968fa4cc7e86689a74050685ac8006c4cc4"
    integrity sha1-pETpaPpMx+hmiadAUGhayABsTMQ=
    dependencies:
      kleur "^3.0.3"
      sisteransi "^1.0.3"
  
  psl@^1.1.24, psl@^1.1.28:
    version "1.5.0"
    resolved "http://r.tnpm.oa.com/psl/download/psl-1.5.0.tgz#47fd1292def7fdb1e138cd78afa8814cebcf7b13"
    integrity sha1-R/0Skt73/bHhOM14r6iBTOvPexM=
  
  pump@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/pump/download/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
    integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
    dependencies:
      end-of-stream "^1.1.0"
      once "^1.3.1"
  
  punycode@1.3.2:
    version "1.3.2"
    resolved "http://r.tnpm.oa.com/punycode/download/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"
    integrity sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=
  
  punycode@^1.4.1:
    version "1.4.1"
    resolved "http://r.tnpm.oa.com/punycode/download/punycode-1.4.1.tgz#c0d5a63b2718800ad8e1eb0fa5269c84dd41845e"
    integrity sha1-wNWmOycYgArY4esPpSachN1BhF4=
  
  punycode@^2.1.0, punycode@^2.1.1:
    version "2.1.1"
    resolved "http://r.tnpm.oa.com/punycode/download/punycode-2.1.1.tgz#b58b010ac40c22c5657616c8d2c2c02c7bf479ec"
    integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=
  
  qs@~6.5.2:
    version "6.5.2"
    resolved "http://r.tnpm.oa.com/qs/download/qs-6.5.2.tgz#cb3ae806e8740444584ef154ce8ee98d403f3e36"
    integrity sha1-yzroBuh0BERYTvFUzo7pjUA/PjY=
  
  querystring@0.2.0:
    version "0.2.0"
    resolved "http://r.tnpm.oa.com/querystring/download/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"
    integrity sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=
  
  rc@^1.2.7:
    version "1.2.8"
    resolved "http://r.tnpm.oa.com/rc/download/rc-1.2.8.tgz#cd924bf5200a075b83c188cd6b9e211b7fc0d3ed"
    integrity sha1-zZJL9SAKB1uDwYjNa54hG3/A0+0=
    dependencies:
      deep-extend "^0.6.0"
      ini "~1.3.0"
      minimist "^1.2.0"
      strip-json-comments "~2.0.1"
  
  react-is@^16.8.4:
    version "16.12.0"
    resolved "http://r.tnpm.oa.com/react-is/download/react-is-16.12.0.tgz#2cc0fe0fba742d97fd527c42a13bec4eeb06241c"
    integrity sha1-LMD+D7p0LZf9UnxCoTvsTusGJBw=
  
  read-pkg-up@^4.0.0:
    version "4.0.0"
    resolved "http://r.tnpm.oa.com/read-pkg-up/download/read-pkg-up-4.0.0.tgz#1b221c6088ba7799601c808f91161c66e58f8978"
    integrity sha1-GyIcYIi6d5lgHICPkRYcZuWPiXg=
    dependencies:
      find-up "^3.0.0"
      read-pkg "^3.0.0"
  
  read-pkg@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/read-pkg/download/read-pkg-3.0.0.tgz#9cbc686978fee65d16c00e2b19c237fcf6e38389"
    integrity sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k=
    dependencies:
      load-json-file "^4.0.0"
      normalize-package-data "^2.3.2"
      path-type "^3.0.0"
  
  read-pkg@^5.2.0:
    version "5.2.0"
    resolved "http://r.tnpm.oa.com/read-pkg/download/read-pkg-5.2.0.tgz#7bf295438ca5a33e56cd30e053b34ee7250c93cc"
    integrity sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=
    dependencies:
      "@types/normalize-package-data" "^2.4.0"
      normalize-package-data "^2.5.0"
      parse-json "^5.0.0"
      type-fest "^0.6.0"
  
  readable-stream@^2.0.6:
    version "2.3.6"
    resolved "http://r.tnpm.oa.com/readable-stream/download/readable-stream-2.3.6.tgz#b11c27d88b8ff1fbe070643cf94b0c79ae1b0aaf"
    integrity sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=
    dependencies:
      core-util-is "~1.0.0"
      inherits "~2.0.3"
      isarray "~1.0.0"
      process-nextick-args "~2.0.0"
      safe-buffer "~5.1.1"
      string_decoder "~1.1.1"
      util-deprecate "~1.0.1"
  
  realpath-native@^1.1.0:
    version "1.1.0"
    resolved "http://r.tnpm.oa.com/realpath-native/download/realpath-native-1.1.0.tgz#2003294fea23fb0672f2476ebe22fcf498a2d65c"
    integrity sha1-IAMpT+oj+wZy8kduviL89Jii1lw=
    dependencies:
      util.promisify "^1.0.0"
  
  regex-not@^1.0.0, regex-not@^1.0.2:
    version "1.0.2"
    resolved "http://r.tnpm.oa.com/regex-not/download/regex-not-1.0.2.tgz#1f4ece27e00b0b65e0247a6810e6a85d83a5752c"
    integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
    dependencies:
      extend-shallow "^3.0.2"
      safe-regex "^1.1.0"
  
  regexpp@^2.0.1:
    version "2.0.1"
    resolved "http://r.tnpm.oa.com/regexpp/download/regexpp-2.0.1.tgz#8d19d31cf632482b589049f8281f93dbcba4d07f"
    integrity sha1-jRnTHPYySCtYkEn4KB+T28uk0H8=
  
  regexpp@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/regexpp/download/regexpp-3.0.0.tgz#dd63982ee3300e67b41c1956f850aa680d9d330e"
    integrity sha1-3WOYLuMwDme0HBlW+FCqaA2dMw4=
  
  remove-trailing-separator@^1.0.1:
    version "1.1.0"
    resolved "http://r.tnpm.oa.com/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz#c24bce2a283adad5bc3f58e0d48249b92379d8ef"
    integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=
  
  repeat-element@^1.1.2:
    version "1.1.3"
    resolved "http://r.tnpm.oa.com/repeat-element/download/repeat-element-1.1.3.tgz#782e0d825c0c5a3bb39731f84efee6b742e6b1ce"
    integrity sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4=
  
  repeat-string@^1.6.1:
    version "1.6.1"
    resolved "http://r.tnpm.oa.com/repeat-string/download/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
    integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=
  
  request-promise-core@1.1.3:
    version "1.1.3"
    resolved "http://r.tnpm.oa.com/request-promise-core/download/request-promise-core-1.1.3.tgz#e9a3c081b51380dfea677336061fea879a829ee9"
    integrity sha1-6aPAgbUTgN/qZ3M2Bh/qh5qCnuk=
    dependencies:
      lodash "^4.17.15"
  
  request-promise-native@^1.0.5:
    version "1.0.8"
    resolved "http://r.tnpm.oa.com/request-promise-native/download/request-promise-native-1.0.8.tgz#a455b960b826e44e2bf8999af64dff2bfe58cb36"
    integrity sha1-pFW5YLgm5E4r+Jma9k3/K/5YyzY=
    dependencies:
      request-promise-core "1.1.3"
      stealthy-require "^1.1.1"
      tough-cookie "^2.3.3"
  
  request@^2.87.0:
    version "2.88.0"
    resolved "http://r.tnpm.oa.com/request/download/request-2.88.0.tgz#9c2fca4f7d35b592efe57c7f0a55e81052124fef"
    integrity sha1-nC/KT301tZLv5Xx/ClXoEFIST+8=
    dependencies:
      aws-sign2 "~0.7.0"
      aws4 "^1.8.0"
      caseless "~0.12.0"
      combined-stream "~1.0.6"
      extend "~3.0.2"
      forever-agent "~0.6.1"
      form-data "~2.3.2"
      har-validator "~5.1.0"
      http-signature "~1.2.0"
      is-typedarray "~1.0.0"
      isstream "~0.1.2"
      json-stringify-safe "~5.0.1"
      mime-types "~2.1.19"
      oauth-sign "~0.9.0"
      performance-now "^2.1.0"
      qs "~6.5.2"
      safe-buffer "^5.1.2"
      tough-cookie "~2.4.3"
      tunnel-agent "^0.6.0"
      uuid "^3.3.2"
  
  require-directory@^2.1.1:
    version "2.1.1"
    resolved "http://r.tnpm.oa.com/require-directory/download/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
    integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=
  
  require-main-filename@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/require-main-filename/download/require-main-filename-2.0.0.tgz#d0b329ecc7cc0f61649f62215be69af54aa8989b"
    integrity sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=
  
  requireindex@~1.1.0:
    version "1.1.0"
    resolved "http://r.tnpm.oa.com/requireindex/download/requireindex-1.1.0.tgz#e5404b81557ef75db6e49c5a72004893fe03e162"
    integrity sha1-5UBLgVV+91225JxacgBIk/4D4WI=
  
  resolve-cwd@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/resolve-cwd/download/resolve-cwd-2.0.0.tgz#00a9f7387556e27038eae232caa372a6a59b665a"
    integrity sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=
    dependencies:
      resolve-from "^3.0.0"
  
  resolve-from@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/resolve-from/download/resolve-from-3.0.0.tgz#b22c7af7d9d6881bc8b6e653335eebcb0a188748"
    integrity sha1-six699nWiBvItuZTM17rywoYh0g=
  
  resolve-from@^4.0.0:
    version "4.0.0"
    resolved "http://r.tnpm.oa.com/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
    integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=
  
  resolve-url@^0.2.1:
    version "0.2.1"
    resolved "http://r.tnpm.oa.com/resolve-url/download/resolve-url-0.2.1.tgz#2c637fe77c893afd2a663fe21aa9080068e2052a"
    integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=
  
  resolve@1.1.7:
    version "1.1.7"
    resolved "http://r.tnpm.oa.com/resolve/download/resolve-1.1.7.tgz#203114d82ad2c5ed9e8e0411b3932875e889e97b"
    integrity sha1-IDEU2CrSxe2ejgQRs5ModeiJ6Xs=
  
  resolve@1.x, resolve@^1.10.0, resolve@^1.3.2:
    version "1.13.1"
    resolved "http://r.tnpm.oa.com/resolve/download/resolve-1.13.1.tgz#be0aa4c06acd53083505abb35f4d66932ab35d16"
    integrity sha1-vgqkwGrNUwg1BauzX01mkyqzXRY=
    dependencies:
      path-parse "^1.0.6"
  
  restore-cursor@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/restore-cursor/download/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
    integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
    dependencies:
      onetime "^2.0.0"
      signal-exit "^3.0.2"
  
  restore-cursor@^3.1.0:
    version "3.1.0"
    resolved "http://r.tnpm.oa.com/restore-cursor/download/restore-cursor-3.1.0.tgz#39f67c54b3a7a58cea5236d95cf0034239631f7e"
    integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
    dependencies:
      onetime "^5.1.0"
      signal-exit "^3.0.2"
  
  ret@~0.1.10:
    version "0.1.15"
    resolved "http://r.tnpm.oa.com/ret/download/ret-0.1.15.tgz#b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc"
    integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=
  
  reusify@^1.0.0:
    version "1.0.4"
    resolved "http://r.tnpm.oa.com/reusify/download/reusify-1.0.4.tgz#90da382b1e126efc02146e90845a88db12925d76"
    integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=
  
  rimraf@2.6.3:
    version "2.6.3"
    resolved "http://r.tnpm.oa.com/rimraf/download/rimraf-2.6.3.tgz#b2d104fe0d8fb27cf9e0a1cda8262dd3833c6cab"
    integrity sha1-stEE/g2Psnz54KHNqCYt04M8bKs=
    dependencies:
      glob "^7.1.3"
  
  rimraf@^2.5.4, rimraf@^2.6.1, rimraf@^2.6.3:
    version "2.7.1"
    resolved "http://r.tnpm.oa.com/rimraf/download/rimraf-2.7.1.tgz#35797f13a7fdadc566142c29d4f07ccad483e3ec"
    integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
    dependencies:
      glob "^7.1.3"
  
  rimraf@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/rimraf/download/rimraf-3.0.0.tgz#614176d4b3010b75e5c390eb0ee96f6dc0cebb9b"
    integrity sha1-YUF21LMBC3Xlw5DrDulvbcDOu5s=
    dependencies:
      glob "^7.1.3"
  
  rsvp@^4.8.4:
    version "4.8.5"
    resolved "http://r.tnpm.oa.com/rsvp/download/rsvp-4.8.5.tgz#c8f155311d167f68f21e168df71ec5b083113734"
    integrity sha1-yPFVMR0Wf2jyHhaN9x7FsIMRNzQ=
  
  run-async@^2.2.0:
    version "2.3.0"
    resolved "http://r.tnpm.oa.com/run-async/download/run-async-2.3.0.tgz#0371ab4ae0bdd720d4166d7dfda64ff7a445a6c0"
    integrity sha1-A3GrSuC91yDUFm19/aZP96RFpsA=
    dependencies:
      is-promise "^2.1.0"
  
  run-node@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/run-node/download/run-node-1.0.0.tgz#46b50b946a2aa2d4947ae1d886e9856fd9cabe5e"
    integrity sha1-RrULlGoqotSUeuHYhumFb9nKvl4=
  
  run-parallel@^1.1.9:
    version "1.1.9"
    resolved "http://r.tnpm.oa.com/run-parallel/download/run-parallel-1.1.9.tgz#c9dd3a7cf9f4b2c4b6244e173a6ed866e61dd679"
    integrity sha1-yd06fPn0ssS2JE4XOm7YZuYd1nk=
  
  rxjs@^6.3.3, rxjs@^6.4.0:
    version "6.5.3"
    resolved "http://r.tnpm.oa.com/rxjs/download/rxjs-6.5.3.tgz#510e26317f4db91a7eb1de77d9dd9ba0a4899a3a"
    integrity sha1-UQ4mMX9NuRp+sd532d2boKSJmjo=
    dependencies:
      tslib "^1.9.0"
  
  safe-buffer@^5.0.1, safe-buffer@^5.1.2:
    version "5.2.0"
    resolved "http://r.tnpm.oa.com/safe-buffer/download/safe-buffer-5.2.0.tgz#b74daec49b1148f88c64b68d49b1e815c1f2f519"
    integrity sha1-t02uxJsRSPiMZLaNSbHoFcHy9Rk=
  
  safe-buffer@~5.1.0, safe-buffer@~5.1.1:
    version "5.1.2"
    resolved "http://r.tnpm.oa.com/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
    integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=
  
  safe-regex@^1.1.0:
    version "1.1.0"
    resolved "http://r.tnpm.oa.com/safe-regex/download/safe-regex-1.1.0.tgz#40a3669f3b077d1e943d44629e157dd48023bf2e"
    integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
    dependencies:
      ret "~0.1.10"
  
  "safer-buffer@>= 2.1.2 < 3", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
    version "2.1.2"
    resolved "http://r.tnpm.oa.com/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
    integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=
  
  sane@^4.0.3:
    version "4.1.0"
    resolved "http://r.tnpm.oa.com/sane/download/sane-4.1.0.tgz#ed881fd922733a6c461bc189dc2b6c006f3ffded"
    integrity sha1-7Ygf2SJzOmxGG8GJ3CtsAG8//e0=
    dependencies:
      "@cnakazawa/watch" "^1.0.3"
      anymatch "^2.0.0"
      capture-exit "^2.0.0"
      exec-sh "^0.3.2"
      execa "^1.0.0"
      fb-watchman "^2.0.0"
      micromatch "^3.1.4"
      minimist "^1.1.1"
      walker "~1.0.5"
  
  sax@^1.2.4:
    version "1.2.4"
    resolved "http://r.tnpm.oa.com/sax/download/sax-1.2.4.tgz#2816234e2378bddc4e5354fab5caa895df7100d9"
    integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=
  
  semver-compare@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/semver-compare/download/semver-compare-1.0.0.tgz#0dee216a1c941ab37e9efb1788f6afc5ff5537fc"
    integrity sha1-De4hahyUGrN+nvsXiPavxf9VN/w=
  
  "semver@2 || 3 || 4 || 5", semver@^5.3.0, semver@^5.4.1, semver@^5.5, semver@^5.5.0, semver@^5.6.0:
    version "5.7.1"
    resolved "http://r.tnpm.oa.com/semver/download/semver-5.7.1.tgz#a954f931aeba508d307bbf069eff0c01c96116f7"
    integrity sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=
  
  semver@5.5.0:
    version "5.5.0"
    resolved "http://r.tnpm.oa.com/semver/download/semver-5.5.0.tgz#dc4bbc7a6ca9d916dee5d43516f0092b58f7b8ab"
    integrity sha1-3Eu8emyp2Rbe5dQ1FvAJK1j3uKs=
  
  semver@^6.0.0, semver@^6.1.2, semver@^6.2.0, semver@^6.3.0:
    version "6.3.0"
    resolved "http://r.tnpm.oa.com/semver/download/semver-6.3.0.tgz#ee0a64c8af5e8ceea67687b133761e1becbd1d3d"
    integrity sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=
  
  set-blocking@^2.0.0, set-blocking@~2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/set-blocking/download/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"
    integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=
  
  set-value@^2.0.0, set-value@^2.0.1:
    version "2.0.1"
    resolved "http://r.tnpm.oa.com/set-value/download/set-value-2.0.1.tgz#a18d40530e6f07de4228c7defe4227af8cad005b"
    integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
    dependencies:
      extend-shallow "^2.0.1"
      is-extendable "^0.1.1"
      is-plain-object "^2.0.3"
      split-string "^3.0.1"
  
  shebang-command@^1.2.0:
    version "1.2.0"
    resolved "http://r.tnpm.oa.com/shebang-command/download/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
    integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
    dependencies:
      shebang-regex "^1.0.0"
  
  shebang-command@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/shebang-command/download/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
    integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
    dependencies:
      shebang-regex "^3.0.0"
  
  shebang-regex@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/shebang-regex/download/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"
    integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=
  
  shebang-regex@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/shebang-regex/download/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
    integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=
  
  shellwords@^0.1.1:
    version "0.1.1"
    resolved "http://r.tnpm.oa.com/shellwords/download/shellwords-0.1.1.tgz#d6b9181c1a48d397324c84871efbcfc73fc0654b"
    integrity sha1-1rkYHBpI05cyTISHHvvPxz/AZUs=
  
  signal-exit@^3.0.0, signal-exit@^3.0.2:
    version "3.0.2"
    resolved "http://r.tnpm.oa.com/signal-exit/download/signal-exit-3.0.2.tgz#b5fdc08f1287ea1178628e415e25132b73646c6d"
    integrity sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0=
  
  sisteransi@^1.0.3:
    version "1.0.4"
    resolved "http://r.tnpm.oa.com/sisteransi/download/sisteransi-1.0.4.tgz#386713f1ef688c7c0304dc4c0632898941cad2e3"
    integrity sha1-OGcT8e9ojHwDBNxMBjKJiUHK0uM=
  
  slash@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/slash/download/slash-2.0.0.tgz#de552851a1759df3a8f206535442f5ec4ddeab44"
    integrity sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=
  
  slash@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/slash/download/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
    integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=
  
  slice-ansi@0.0.4:
    version "0.0.4"
    resolved "http://r.tnpm.oa.com/slice-ansi/download/slice-ansi-0.0.4.tgz#edbf8903f66f7ce2f8eafd6ceed65e264c831b35"
    integrity sha1-7b+JA/ZvfOL46v1s7tZeJkyDGzU=
  
  slice-ansi@^2.1.0:
    version "2.1.0"
    resolved "http://r.tnpm.oa.com/slice-ansi/download/slice-ansi-2.1.0.tgz#cacd7693461a637a5788d92a7dd4fba068e81636"
    integrity sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY=
    dependencies:
      ansi-styles "^3.2.0"
      astral-regex "^1.0.0"
      is-fullwidth-code-point "^2.0.0"
  
  snapdragon-node@^2.0.1:
    version "2.1.1"
    resolved "http://r.tnpm.oa.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz#6c175f86ff14bdb0724563e8f3c1b021a286853b"
    integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
    dependencies:
      define-property "^1.0.0"
      isobject "^3.0.0"
      snapdragon-util "^3.0.1"
  
  snapdragon-util@^3.0.1:
    version "3.0.1"
    resolved "http://r.tnpm.oa.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz#f956479486f2acd79700693f6f7b805e45ab56e2"
    integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
    dependencies:
      kind-of "^3.2.0"
  
  snapdragon@^0.8.1:
    version "0.8.2"
    resolved "http://r.tnpm.oa.com/snapdragon/download/snapdragon-0.8.2.tgz#64922e7c565b0e14204ba1aa7d6964278d25182d"
    integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
    dependencies:
      base "^0.11.1"
      debug "^2.2.0"
      define-property "^0.2.5"
      extend-shallow "^2.0.1"
      map-cache "^0.2.2"
      source-map "^0.5.6"
      source-map-resolve "^0.5.0"
      use "^3.1.0"
  
  source-map-resolve@^0.5.0:
    version "0.5.2"
    resolved "http://r.tnpm.oa.com/source-map-resolve/download/source-map-resolve-0.5.2.tgz#72e2cc34095543e43b2c62b2c4c10d4a9054f259"
    integrity sha1-cuLMNAlVQ+Q7LGKyxMENSpBU8lk=
    dependencies:
      atob "^2.1.1"
      decode-uri-component "^0.2.0"
      resolve-url "^0.2.1"
      source-map-url "^0.4.0"
      urix "^0.1.0"
  
  source-map-support@^0.5.6:
    version "0.5.16"
    resolved "http://r.tnpm.oa.com/source-map-support/download/source-map-support-0.5.16.tgz#0ae069e7fe3ba7538c64c98515e35339eac5a042"
    integrity sha1-CuBp5/47p1OMZMmFFeNTOerFoEI=
    dependencies:
      buffer-from "^1.0.0"
      source-map "^0.6.0"
  
  source-map-url@^0.4.0:
    version "0.4.0"
    resolved "http://r.tnpm.oa.com/source-map-url/download/source-map-url-0.4.0.tgz#3e935d7ddd73631b97659956d55128e87b5084a3"
    integrity sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=
  
  source-map@^0.5.0, source-map@^0.5.6:
    version "0.5.7"
    resolved "http://r.tnpm.oa.com/source-map/download/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
    integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=
  
  source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.1:
    version "0.6.1"
    resolved "http://r.tnpm.oa.com/source-map/download/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
    integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=
  
  spdx-correct@^3.0.0:
    version "3.1.0"
    resolved "http://r.tnpm.oa.com/spdx-correct/download/spdx-correct-3.1.0.tgz#fb83e504445268f154b074e218c87c003cd31df4"
    integrity sha1-+4PlBERSaPFUsHTiGMh8ADzTHfQ=
    dependencies:
      spdx-expression-parse "^3.0.0"
      spdx-license-ids "^3.0.0"
  
  spdx-exceptions@^2.1.0:
    version "2.2.0"
    resolved "http://r.tnpm.oa.com/spdx-exceptions/download/spdx-exceptions-2.2.0.tgz#2ea450aee74f2a89bfb94519c07fcd6f41322977"
    integrity sha1-LqRQrudPKom/uUUZwH/Nb0EyKXc=
  
  spdx-expression-parse@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/spdx-expression-parse/download/spdx-expression-parse-3.0.0.tgz#99e119b7a5da00e05491c9fa338b7904823b41d0"
    integrity sha1-meEZt6XaAOBUkcn6M4t5BII7QdA=
    dependencies:
      spdx-exceptions "^2.1.0"
      spdx-license-ids "^3.0.0"
  
  spdx-license-ids@^3.0.0:
    version "3.0.5"
    resolved "http://r.tnpm.oa.com/spdx-license-ids/download/spdx-license-ids-3.0.5.tgz#3694b5804567a458d3c8045842a6358632f62654"
    integrity sha1-NpS1gEVnpFjTyARYQqY1hjL2JlQ=
  
  split-string@^3.0.1, split-string@^3.0.2:
    version "3.1.0"
    resolved "http://r.tnpm.oa.com/split-string/download/split-string-3.1.0.tgz#7cb09dda3a86585705c64b39a6466038682e8fe2"
    integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
    dependencies:
      extend-shallow "^3.0.0"
  
  sprintf-js@~1.0.2:
    version "1.0.3"
    resolved "http://r.tnpm.oa.com/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
    integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=
  
  sshpk@^1.7.0:
    version "1.16.1"
    resolved "http://r.tnpm.oa.com/sshpk/download/sshpk-1.16.1.tgz#fb661c0bef29b39db40769ee39fa70093d6f6877"
    integrity sha1-+2YcC+8ps520B2nuOfpwCT1vaHc=
    dependencies:
      asn1 "~0.2.3"
      assert-plus "^1.0.0"
      bcrypt-pbkdf "^1.0.0"
      dashdash "^1.12.0"
      ecc-jsbn "~0.1.1"
      getpass "^0.1.1"
      jsbn "~0.1.0"
      safer-buffer "^2.0.2"
      tweetnacl "~0.14.0"
  
  stack-utils@^1.0.1:
    version "1.0.2"
    resolved "http://r.tnpm.oa.com/stack-utils/download/stack-utils-1.0.2.tgz#33eba3897788558bebfc2db059dc158ec36cebb8"
    integrity sha1-M+ujiXeIVYvr/C2wWdwVjsNs67g=
  
  static-extend@^0.1.1:
    version "0.1.2"
    resolved "http://r.tnpm.oa.com/static-extend/download/static-extend-0.1.2.tgz#60809c39cbff55337226fd5e0b520f341f1fb5c6"
    integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
    dependencies:
      define-property "^0.2.5"
      object-copy "^0.1.0"
  
  stealthy-require@^1.1.1:
    version "1.1.1"
    resolved "http://r.tnpm.oa.com/stealthy-require/download/stealthy-require-1.1.1.tgz#35b09875b4ff49f26a777e509b3090a3226bf24b"
    integrity sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks=
  
  string-argv@^0.3.0:
    version "0.3.1"
    resolved "http://r.tnpm.oa.com/string-argv/download/string-argv-0.3.1.tgz#95e2fbec0427ae19184935f816d74aaa4c5c19da"
    integrity sha1-leL77AQnrhkYSTX4FtdKqkxcGdo=
  
  string-length@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/string-length/download/string-length-2.0.0.tgz#d40dbb686a3ace960c1cffca562bf2c45f8363ed"
    integrity sha1-1A27aGo6zpYMHP/KVivyxF+DY+0=
    dependencies:
      astral-regex "^1.0.0"
      strip-ansi "^4.0.0"
  
  string-width@^1.0.1:
    version "1.0.2"
    resolved "http://r.tnpm.oa.com/string-width/download/string-width-1.0.2.tgz#118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3"
    integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
    dependencies:
      code-point-at "^1.0.0"
      is-fullwidth-code-point "^1.0.0"
      strip-ansi "^3.0.0"
  
  "string-width@^1.0.2 || 2", string-width@^2.1.1:
    version "2.1.1"
    resolved "http://r.tnpm.oa.com/string-width/download/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
    integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
    dependencies:
      is-fullwidth-code-point "^2.0.0"
      strip-ansi "^4.0.0"
  
  string-width@^3.0.0, string-width@^3.1.0:
    version "3.1.0"
    resolved "http://r.tnpm.oa.com/string-width/download/string-width-3.1.0.tgz#22767be21b62af1081574306f69ac51b62203961"
    integrity sha1-InZ74htirxCBV0MG9prFG2IgOWE=
    dependencies:
      emoji-regex "^7.0.1"
      is-fullwidth-code-point "^2.0.0"
      strip-ansi "^5.1.0"
  
  string-width@^4.1.0:
    version "4.2.0"
    resolved "http://r.tnpm.oa.com/string-width/download/string-width-4.2.0.tgz#952182c46cc7b2c313d1596e623992bd163b72b5"
    integrity sha1-lSGCxGzHssMT0VluYjmSvRY7crU=
    dependencies:
      emoji-regex "^8.0.0"
      is-fullwidth-code-point "^3.0.0"
      strip-ansi "^6.0.0"
  
  string.prototype.trimleft@^2.1.0:
    version "2.1.0"
    resolved "http://r.tnpm.oa.com/string.prototype.trimleft/download/string.prototype.trimleft-2.1.0.tgz#6cc47f0d7eb8d62b0f3701611715a3954591d634"
    integrity sha1-bMR/DX641isPNwFhFxWjlUWR1jQ=
    dependencies:
      define-properties "^1.1.3"
      function-bind "^1.1.1"
  
  string.prototype.trimright@^2.1.0:
    version "2.1.0"
    resolved "http://r.tnpm.oa.com/string.prototype.trimright/download/string.prototype.trimright-2.1.0.tgz#669d164be9df9b6f7559fa8e89945b168a5a6c58"
    integrity sha1-Zp0WS+nfm291WfqOiZRbFopabFg=
    dependencies:
      define-properties "^1.1.3"
      function-bind "^1.1.1"
  
  string_decoder@~1.1.1:
    version "1.1.1"
    resolved "http://r.tnpm.oa.com/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
    integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
    dependencies:
      safe-buffer "~5.1.0"
  
  stringify-object@^3.3.0:
    version "3.3.0"
    resolved "http://r.tnpm.oa.com/stringify-object/download/stringify-object-3.3.0.tgz#703065aefca19300d3ce88af4f5b3956d7556629"
    integrity sha1-cDBlrvyhkwDTzoivT1s5VtdVZik=
    dependencies:
      get-own-enumerable-property-symbols "^3.0.0"
      is-obj "^1.0.1"
      is-regexp "^1.0.0"
  
  strip-ansi@^3.0.0, strip-ansi@^3.0.1:
    version "3.0.1"
    resolved "http://r.tnpm.oa.com/strip-ansi/download/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
    integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
    dependencies:
      ansi-regex "^2.0.0"
  
  strip-ansi@^4.0.0:
    version "4.0.0"
    resolved "http://r.tnpm.oa.com/strip-ansi/download/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
    integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
    dependencies:
      ansi-regex "^3.0.0"
  
  strip-ansi@^5.0.0, strip-ansi@^5.1.0, strip-ansi@^5.2.0:
    version "5.2.0"
    resolved "http://r.tnpm.oa.com/strip-ansi/download/strip-ansi-5.2.0.tgz#8c9a536feb6afc962bdfa5b104a5091c1ad9c0ae"
    integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
    dependencies:
      ansi-regex "^4.1.0"
  
  strip-ansi@^6.0.0:
    version "6.0.0"
    resolved "http://r.tnpm.oa.com/strip-ansi/download/strip-ansi-6.0.0.tgz#0b1571dd7669ccd4f3e06e14ef1eed26225ae532"
    integrity sha1-CxVx3XZpzNTz4G4U7x7tJiJa5TI=
    dependencies:
      ansi-regex "^5.0.0"
  
  strip-bom@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/strip-bom/download/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"
    integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=
  
  strip-eof@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/strip-eof/download/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"
    integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=
  
  strip-final-newline@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz#89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad"
    integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=
  
  strip-json-comments@^3.0.1:
    version "3.0.1"
    resolved "http://r.tnpm.oa.com/strip-json-comments/download/strip-json-comments-3.0.1.tgz#85713975a91fb87bf1b305cca77395e40d2a64a7"
    integrity sha1-hXE5dakfuHvxswXMp3OV5A0qZKc=
  
  strip-json-comments@~2.0.1:
    version "2.0.1"
    resolved "http://r.tnpm.oa.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz#3c531942e908c2697c0ec344858c286c7ca0a60a"
    integrity sha1-PFMZQukIwml8DsNEhYwobHygpgo=
  
  supports-color@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/supports-color/download/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"
    integrity sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=
  
  supports-color@^5.3.0:
    version "5.5.0"
    resolved "http://r.tnpm.oa.com/supports-color/download/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
    integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
    dependencies:
      has-flag "^3.0.0"
  
  supports-color@^6.1.0:
    version "6.1.0"
    resolved "http://r.tnpm.oa.com/supports-color/download/supports-color-6.1.0.tgz#0764abc69c63d5ac842dd4867e8d025e880df8f3"
    integrity sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=
    dependencies:
      has-flag "^3.0.0"
  
  symbol-observable@^1.1.0:
    version "1.2.0"
    resolved "http://r.tnpm.oa.com/symbol-observable/download/symbol-observable-1.2.0.tgz#c22688aed4eab3cdc2dfeacbb561660560a00804"
    integrity sha1-wiaIrtTqs83C3+rLtWFmBWCgCAQ=
  
  symbol-tree@^3.2.2:
    version "3.2.4"
    resolved "http://r.tnpm.oa.com/symbol-tree/download/symbol-tree-3.2.4.tgz#430637d248ba77e078883951fb9aa0eed7c63fa2"
    integrity sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=
  
  table@^5.2.3:
    version "5.4.6"
    resolved "http://r.tnpm.oa.com/table/download/table-5.4.6.tgz#1292d19500ce3f86053b05f0e8e7e4a3bb21079e"
    integrity sha1-EpLRlQDOP4YFOwXw6Ofko7shB54=
    dependencies:
      ajv "^6.10.2"
      lodash "^4.17.14"
      slice-ansi "^2.1.0"
      string-width "^3.0.0"
  
  tar@^4:
    version "4.4.13"
    resolved "http://r.tnpm.oa.com/tar/download/tar-4.4.13.tgz#43b364bc52888d555298637b10d60790254ab525"
    integrity sha1-Q7NkvFKIjVVSmGN7ENYHkCVKtSU=
    dependencies:
      chownr "^1.1.1"
      fs-minipass "^1.2.5"
      minipass "^2.8.6"
      minizlib "^1.2.1"
      mkdirp "^0.5.0"
      safe-buffer "^5.1.2"
      yallist "^3.0.3"
  
  test-exclude@^5.2.3:
    version "5.2.3"
    resolved "http://r.tnpm.oa.com/test-exclude/download/test-exclude-5.2.3.tgz#c3d3e1e311eb7ee405e092dac10aefd09091eac0"
    integrity sha1-w9Ph4xHrfuQF4JLawQrv0JCR6sA=
    dependencies:
      glob "^7.1.3"
      minimatch "^3.0.4"
      read-pkg-up "^4.0.0"
      require-main-filename "^2.0.0"
  
  text-table@^0.2.0:
    version "0.2.0"
    resolved "http://r.tnpm.oa.com/text-table/download/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
    integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=
  
  throat@^4.0.0:
    version "4.1.0"
    resolved "http://r.tnpm.oa.com/throat/download/throat-4.1.0.tgz#89037cbc92c56ab18926e6ba4cbb200e15672a6a"
    integrity sha1-iQN8vJLFarGJJua6TLsgDhVnKmo=
  
  through@^2.3.6:
    version "2.3.8"
    resolved "http://r.tnpm.oa.com/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
    integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=
  
  tmp@^0.0.33:
    version "0.0.33"
    resolved "http://r.tnpm.oa.com/tmp/download/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
    integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
    dependencies:
      os-tmpdir "~1.0.2"
  
  tmpl@1.0.x:
    version "1.0.4"
    resolved "http://r.tnpm.oa.com/tmpl/download/tmpl-1.0.4.tgz#23640dd7b42d00433911140820e5cf440e521dd1"
    integrity sha1-I2QN17QtAEM5ERQIIOXPRA5SHdE=
  
  to-fast-properties@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/to-fast-properties/download/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
    integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=
  
  to-object-path@^0.3.0:
    version "0.3.0"
    resolved "http://r.tnpm.oa.com/to-object-path/download/to-object-path-0.3.0.tgz#297588b7b0e7e0ac08e04e672f85c1f4999e17af"
    integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
    dependencies:
      kind-of "^3.0.2"
  
  to-regex-range@^2.1.0:
    version "2.1.1"
    resolved "http://r.tnpm.oa.com/to-regex-range/download/to-regex-range-2.1.1.tgz#7c80c17b9dfebe599e27367e0d4dd5590141db38"
    integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
    dependencies:
      is-number "^3.0.0"
      repeat-string "^1.6.1"
  
  to-regex-range@^5.0.1:
    version "5.0.1"
    resolved "http://r.tnpm.oa.com/to-regex-range/download/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
    integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
    dependencies:
      is-number "^7.0.0"
  
  to-regex@^3.0.1, to-regex@^3.0.2:
    version "3.0.2"
    resolved "http://r.tnpm.oa.com/to-regex/download/to-regex-3.0.2.tgz#13cfdd9b336552f30b51f33a8ae1b42a7a7599ce"
    integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
    dependencies:
      define-property "^2.0.2"
      extend-shallow "^3.0.2"
      regex-not "^1.0.2"
      safe-regex "^1.1.0"
  
  tough-cookie@^2.3.3, tough-cookie@^2.3.4:
    version "2.5.0"
    resolved "http://r.tnpm.oa.com/tough-cookie/download/tough-cookie-2.5.0.tgz#cd9fb2a0aa1d5a12b473bd9fb96fa3dcff65ade2"
    integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
    dependencies:
      psl "^1.1.28"
      punycode "^2.1.1"
  
  tough-cookie@~2.4.3:
    version "2.4.3"
    resolved "http://r.tnpm.oa.com/tough-cookie/download/tough-cookie-2.4.3.tgz#53f36da3f47783b0925afa06ff9f3b165280f781"
    integrity sha1-U/Nto/R3g7CSWvoG/587FlKA94E=
    dependencies:
      psl "^1.1.24"
      punycode "^1.4.1"
  
  tr46@^1.0.1:
    version "1.0.1"
    resolved "http://r.tnpm.oa.com/tr46/download/tr46-1.0.1.tgz#a8b13fd6bfd2489519674ccde55ba3693b706d09"
    integrity sha1-qLE/1r/SSJUZZ0zN5VujaTtwbQk=
    dependencies:
      punycode "^2.1.0"
  
  ts-jest@^24.2.0:
    version "24.2.0"
    resolved "http://r.tnpm.oa.com/ts-jest/download/ts-jest-24.2.0.tgz#7abca28c2b4b0a1fdd715cd667d65d047ea4e768"
    integrity sha1-eryijCtLCh/dcVzWZ9ZdBH6k52g=
    dependencies:
      bs-logger "0.x"
      buffer-from "1.x"
      fast-json-stable-stringify "2.x"
      json5 "2.x"
      lodash.memoize "4.x"
      make-error "1.x"
      mkdirp "0.x"
      resolve "1.x"
      semver "^5.5"
      yargs-parser "10.x"
  
  tslib@^1.8.1, tslib@^1.9.0:
    version "1.10.0"
    resolved "http://r.tnpm.oa.com/tslib/download/tslib-1.10.0.tgz#c3c19f95973fb0a62973fb09d90d961ee43e5c8a"
    integrity sha1-w8GflZc/sKYpc/sJ2Q2WHuQ+XIo=
  
  tsutils@^3.17.1:
    version "3.17.1"
    resolved "http://r.tnpm.oa.com/tsutils/download/tsutils-3.17.1.tgz#ed719917f11ca0dee586272b2ac49e015a2dd759"
    integrity sha1-7XGZF/EcoN7lhicrKsSeAVot11k=
    dependencies:
      tslib "^1.8.1"
  
  tunnel-agent@^0.6.0:
    version "0.6.0"
    resolved "http://r.tnpm.oa.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
    integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
    dependencies:
      safe-buffer "^5.0.1"
  
  tweetnacl@^0.14.3, tweetnacl@~0.14.0:
    version "0.14.5"
    resolved "http://r.tnpm.oa.com/tweetnacl/download/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"
    integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=
  
  type-check@~0.3.2:
    version "0.3.2"
    resolved "http://r.tnpm.oa.com/type-check/download/type-check-0.3.2.tgz#5884cab512cf1d355e3fb784f30804b2b520db72"
    integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
    dependencies:
      prelude-ls "~1.1.2"
  
  type-fest@^0.6.0:
    version "0.6.0"
    resolved "http://r.tnpm.oa.com/type-fest/download/type-fest-0.6.0.tgz#8d2a2370d3df886eb5c90ada1c5bf6188acf838b"
    integrity sha1-jSojcNPfiG61yQraHFv2GIrPg4s=
  
  type-fest@^0.8.1:
    version "0.8.1"
    resolved "http://r.tnpm.oa.com/type-fest/download/type-fest-0.8.1.tgz#09e249ebde851d3b1e48d27c105444667f17b83d"
    integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=
  
  typescript-eslint-parser@^22.0.0:
    version "22.0.0"
    resolved "http://r.tnpm.oa.com/typescript-eslint-parser/download/typescript-eslint-parser-22.0.0.tgz#f5e766c9b50711b03535e29a10b45f957e3c516a"
    integrity sha1-9edmybUHEbA1NeKaELRflX48UWo=
    dependencies:
      eslint-scope "^4.0.0"
      eslint-visitor-keys "^1.0.0"
      typescript-estree "18.0.0"
  
  typescript-estree@18.0.0:
    version "18.0.0"
    resolved "http://r.tnpm.oa.com/typescript-estree/download/typescript-estree-18.0.0.tgz#a309f6c6502c64d74b3f88c205d871a9af0b1d40"
    integrity sha1-own2xlAsZNdLP4jCBdhxqa8LHUA=
    dependencies:
      lodash.unescape "4.0.1"
      semver "5.5.0"
  
  typescript@3.5.3:
    version "3.5.3"
    resolved "http://r.tnpm.oa.com/typescript/download/typescript-3.5.3.tgz#c830f657f93f1ea846819e929092f5fe5983e977"
    integrity sha1-yDD2V/k/HqhGgZ6SkJL1/lmD6Xc=
  
  uglify-js@^3.1.4:
    version "3.7.1"
    resolved "http://r.tnpm.oa.com/uglify-js/download/uglify-js-3.7.1.tgz#35c7de17971a4aa7689cd2eae0a5b39bb838c0c5"
    integrity sha1-NcfeF5caSqdonNLq4KWzm7g4wMU=
    dependencies:
      commander "~2.20.3"
      source-map "~0.6.1"
  
  union-value@^1.0.0:
    version "1.0.1"
    resolved "http://r.tnpm.oa.com/union-value/download/union-value-1.0.1.tgz#0b6fe7b835aecda61c6ea4d4f02c14221e109847"
    integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
    dependencies:
      arr-union "^3.1.0"
      get-value "^2.0.6"
      is-extendable "^0.1.1"
      set-value "^2.0.1"
  
  unset-value@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/unset-value/download/unset-value-1.0.0.tgz#8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559"
    integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
    dependencies:
      has-value "^0.3.1"
      isobject "^3.0.0"
  
  uri-js@^4.2.2:
    version "4.2.2"
    resolved "http://r.tnpm.oa.com/uri-js/download/uri-js-4.2.2.tgz#94c540e1ff772956e2299507c010aea6c8838eb0"
    integrity sha1-lMVA4f93KVbiKZUHwBCupsiDjrA=
    dependencies:
      punycode "^2.1.0"
  
  urix@^0.1.0:
    version "0.1.0"
    resolved "http://r.tnpm.oa.com/urix/download/urix-0.1.0.tgz#da937f7a62e21fec1fd18d49b35c2935067a6c72"
    integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=
  
  url@^0.11.0:
    version "0.11.0"
    resolved "http://r.tnpm.oa.com/url/download/url-0.11.0.tgz#3838e97cfc60521eb73c525a8e55bfdd9e2e28f1"
    integrity sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=
    dependencies:
      punycode "1.3.2"
      querystring "0.2.0"
  
  use@^3.1.0:
    version "3.1.1"
    resolved "http://r.tnpm.oa.com/use/download/use-3.1.1.tgz#d50c8cac79a19fbc20f2911f56eb973f4e10070f"
    integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=
  
  util-deprecate@~1.0.1:
    version "1.0.2"
    resolved "http://r.tnpm.oa.com/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
    integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=
  
  util.promisify@^1.0.0:
    version "1.0.0"
    resolved "http://r.tnpm.oa.com/util.promisify/download/util.promisify-1.0.0.tgz#440f7165a459c9a16dc145eb8e72f35687097030"
    integrity sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA=
    dependencies:
      define-properties "^1.1.2"
      object.getownpropertydescriptors "^2.0.3"
  
  uuid@^3.3.2:
    version "3.3.3"
    resolved "http://r.tnpm.oa.com/uuid/download/uuid-3.3.3.tgz#4568f0216e78760ee1dbf3a4d2cf53e224112866"
    integrity sha1-RWjwIW54dg7h2/Ok0s9T4iQRKGY=
  
  v8-compile-cache@^2.0.3:
    version "2.1.0"
    resolved "http://r.tnpm.oa.com/v8-compile-cache/download/v8-compile-cache-2.1.0.tgz#e14de37b31a6d194f5690d67efc4e7f6fc6ab30e"
    integrity sha1-4U3jezGm0ZT1aQ1n78Tn9vxqsw4=
  
  validate-npm-package-license@^3.0.1:
    version "3.0.4"
    resolved "http://r.tnpm.oa.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
    integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
    dependencies:
      spdx-correct "^3.0.0"
      spdx-expression-parse "^3.0.0"
  
  verror@1.10.0:
    version "1.10.0"
    resolved "http://r.tnpm.oa.com/verror/download/verror-1.10.0.tgz#3a105ca17053af55d6e270c1f8288682e18da400"
    integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
    dependencies:
      assert-plus "^1.0.0"
      core-util-is "1.0.2"
      extsprintf "^1.2.0"
  
  w3c-hr-time@^1.0.1:
    version "1.0.1"
    resolved "http://r.tnpm.oa.com/w3c-hr-time/download/w3c-hr-time-1.0.1.tgz#82ac2bff63d950ea9e3189a58a65625fedf19045"
    integrity sha1-gqwr/2PZUOqeMYmlimViX+3xkEU=
    dependencies:
      browser-process-hrtime "^0.1.2"
  
  walker@^1.0.7, walker@~1.0.5:
    version "1.0.7"
    resolved "http://r.tnpm.oa.com/walker/download/walker-1.0.7.tgz#2f7f9b8fd10d677262b18a884e28d19618e028fb"
    integrity sha1-L3+bj9ENZ3JisYqITijRlhjgKPs=
    dependencies:
      makeerror "1.0.x"
  
  webidl-conversions@^4.0.2:
    version "4.0.2"
    resolved "http://r.tnpm.oa.com/webidl-conversions/download/webidl-conversions-4.0.2.tgz#a855980b1f0b6b359ba1d5d9fb39ae941faa63ad"
    integrity sha1-qFWYCx8LazWbodXZ+zmulB+qY60=
  
  whatwg-encoding@^1.0.1, whatwg-encoding@^1.0.3:
    version "1.0.5"
    resolved "http://r.tnpm.oa.com/whatwg-encoding/download/whatwg-encoding-1.0.5.tgz#5abacf777c32166a51d085d6b4f3e7d27113ddb0"
    integrity sha1-WrrPd3wyFmpR0IXWtPPn0nET3bA=
    dependencies:
      iconv-lite "0.4.24"
  
  whatwg-mimetype@^2.1.0, whatwg-mimetype@^2.2.0:
    version "2.3.0"
    resolved "http://r.tnpm.oa.com/whatwg-mimetype/download/whatwg-mimetype-2.3.0.tgz#3d4b1e0312d2079879f826aff18dbeeca5960fbf"
    integrity sha1-PUseAxLSB5h5+Cav8Y2+7KWWD78=
  
  whatwg-url@^6.4.1:
    version "6.5.0"
    resolved "http://r.tnpm.oa.com/whatwg-url/download/whatwg-url-6.5.0.tgz#f2df02bff176fd65070df74ad5ccbb5a199965a8"
    integrity sha1-8t8Cv/F2/WUHDfdK1cy7WhmZZag=
    dependencies:
      lodash.sortby "^4.7.0"
      tr46 "^1.0.1"
      webidl-conversions "^4.0.2"
  
  whatwg-url@^7.0.0:
    version "7.1.0"
    resolved "http://r.tnpm.oa.com/whatwg-url/download/whatwg-url-7.1.0.tgz#c2c492f1eca612988efd3d2266be1b9fc6170d06"
    integrity sha1-wsSS8eymEpiO/T0iZr4bn8YXDQY=
    dependencies:
      lodash.sortby "^4.7.0"
      tr46 "^1.0.1"
      webidl-conversions "^4.0.2"
  
  which-module@^2.0.0:
    version "2.0.0"
    resolved "http://r.tnpm.oa.com/which-module/download/which-module-2.0.0.tgz#d9ef07dce77b9902b8a3a8fa4b31c3e3f7e6e87a"
    integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=
  
  which@^1.2.9, which@^1.3.0:
    version "1.3.1"
    resolved "http://r.tnpm.oa.com/which/download/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
    integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
    dependencies:
      isexe "^2.0.0"
  
  which@^2.0.1:
    version "2.0.2"
    resolved "http://r.tnpm.oa.com/which/download/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
    integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
    dependencies:
      isexe "^2.0.0"
  
  wide-align@^1.1.0:
    version "1.1.3"
    resolved "http://r.tnpm.oa.com/wide-align/download/wide-align-1.1.3.tgz#ae074e6bdc0c14a431e804e624549c633b000457"
    integrity sha1-rgdOa9wMFKQx6ATmJFScYzsABFc=
    dependencies:
      string-width "^1.0.2 || 2"
  
  word-wrap@~1.2.3:
    version "1.2.3"
    resolved "http://r.tnpm.oa.com/word-wrap/download/word-wrap-1.2.3.tgz#610636f6b1f703891bd34771ccb17fb93b47079c"
    integrity sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=
  
  wordwrap@~0.0.2:
    version "0.0.3"
    resolved "http://r.tnpm.oa.com/wordwrap/download/wordwrap-0.0.3.tgz#a3d5da6cd5c0bc0008d37234bbaf1bed63059107"
    integrity sha1-o9XabNXAvAAI03I0u68b7WMFkQc=
  
  wrap-ansi@^3.0.1:
    version "3.0.1"
    resolved "http://r.tnpm.oa.com/wrap-ansi/download/wrap-ansi-3.0.1.tgz#288a04d87eda5c286e060dfe8f135ce8d007f8ba"
    integrity sha1-KIoE2H7aXChuBg3+jxNc6NAH+Lo=
    dependencies:
      string-width "^2.1.1"
      strip-ansi "^4.0.0"
  
  wrap-ansi@^5.1.0:
    version "5.1.0"
    resolved "http://r.tnpm.oa.com/wrap-ansi/download/wrap-ansi-5.1.0.tgz#1fd1f67235d5b6d0fee781056001bfb694c03b09"
    integrity sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=
    dependencies:
      ansi-styles "^3.2.0"
      string-width "^3.0.0"
      strip-ansi "^5.0.0"
  
  wrappy@1:
    version "1.0.2"
    resolved "http://r.tnpm.oa.com/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
    integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=
  
  write-file-atomic@2.4.1:
    version "2.4.1"
    resolved "http://r.tnpm.oa.com/write-file-atomic/download/write-file-atomic-2.4.1.tgz#d0b05463c188ae804396fd5ab2a370062af87529"
    integrity sha1-0LBUY8GIroBDlv1asqNwBir4dSk=
    dependencies:
      graceful-fs "^4.1.11"
      imurmurhash "^0.1.4"
      signal-exit "^3.0.2"
  
  write@1.0.3:
    version "1.0.3"
    resolved "http://r.tnpm.oa.com/write/download/write-1.0.3.tgz#0800e14523b923a387e415123c865616aae0f5c3"
    integrity sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM=
    dependencies:
      mkdirp "^0.5.1"
  
  ws@^5.2.0:
    version "5.2.2"
    resolved "http://r.tnpm.oa.com/ws/download/ws-5.2.2.tgz#dffef14866b8e8dc9133582514d1befaf96e980f"
    integrity sha1-3/7xSGa46NyRM1glFNG++vlumA8=
    dependencies:
      async-limiter "~1.0.0"
  
  xml-name-validator@^3.0.0:
    version "3.0.0"
    resolved "http://r.tnpm.oa.com/xml-name-validator/download/xml-name-validator-3.0.0.tgz#6ae73e06de4d8c6e47f9fb181f78d648ad457c6a"
    integrity sha1-auc+Bt5NjG5H+fsYH3jWSK1FfGo=
  
  y18n@^4.0.0:
    version "4.0.0"
    resolved "http://r.tnpm.oa.com/y18n/download/y18n-4.0.0.tgz#95ef94f85ecc81d007c264e190a120f0a3c8566b"
    integrity sha1-le+U+F7MgdAHwmThkKEg8KPIVms=
  
  yallist@^3.0.0, yallist@^3.0.3:
    version "3.1.1"
    resolved "http://r.tnpm.oa.com/yallist/download/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
    integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=
  
  yargs-parser@10.x:
    version "10.1.0"
    resolved "http://r.tnpm.oa.com/yargs-parser/download/yargs-parser-10.1.0.tgz#7202265b89f7e9e9f2e5765e0fe735a905edbaa8"
    integrity sha1-cgImW4n36eny5XZeD+c1qQXtuqg=
    dependencies:
      camelcase "^4.1.0"
  
  yargs-parser@^13.1.1:
    version "13.1.1"
    resolved "http://r.tnpm.oa.com/yargs-parser/download/yargs-parser-13.1.1.tgz#d26058532aa06d365fe091f6a1fc06b2f7e5eca0"
    integrity sha1-0mBYUyqgbTZf4JH2ofwGsvfl7KA=
    dependencies:
      camelcase "^5.0.0"
      decamelize "^1.2.0"
  
  yargs@^13.3.0:
    version "13.3.0"
    resolved "http://r.tnpm.oa.com/yargs/download/yargs-13.3.0.tgz#4c657a55e07e5f2cf947f8a366567c04a0dedc83"
    integrity sha1-TGV6VeB+Xyz5R/ijZlZ8BKDe3IM=
    dependencies:
      cliui "^5.0.0"
      find-up "^3.0.0"
      get-caller-file "^2.0.1"
      require-directory "^2.1.1"
      require-main-filename "^2.0.0"
      set-blocking "^2.0.0"
      string-width "^3.0.0"
      which-module "^2.0.0"
      y18n "^4.0.0"
      yargs-parser "^13.1.1"
