<template>
  <div class="profile-page">
    <div class="profile-header">
      <div class="avatar-container">
        <el-avatar :size="80" :src="userInfo.avatar || defaultAvatar" />
        <div class="edit-avatar">
          <el-icon><Edit /></el-icon>
        </div>
      </div>
      <div class="user-info">
        <h1>{{ userInfo.name }}</h1>
        <p class="user-email">{{ userInfo.email }}</p>
      </div>
    </div>

    <div class="stats-card">
      <div class="stat-item">
        <div class="stat-value">{{ userStats.completedTasks }}</div>
        <div class="stat-label">已完成任务</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ userStats.totalPoints }}</div>
        <div class="stat-label">累计积分</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ userStats.repairStones }}</div>
        <div class="stat-label">修复石</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ userStats.streak }}天</div>
        <div class="stat-label">连续学习</div>
      </div>
    </div>

    <div class="profile-section">
      <div class="section-header">
        <h2>个人信息</h2>
        <el-button type="primary" plain size="small" @click="editProfile">编辑</el-button>
      </div>
      <div class="info-list">
        <div class="info-item">
          <span class="info-label">姓名</span>
          <span class="info-value">{{ userInfo.name }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">邮箱</span>
          <span class="info-value">{{ userInfo.email }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">手机</span>
          <span class="info-value">{{ userInfo.phone || '未设置' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">目标院校</span>
          <span class="info-value">{{ university ? university.name : '未设置' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">目标专业</span>
          <span class="info-value">{{ university ? university.department : '未设置' }}</span>
        </div>
      </div>
    </div>

    <div class="profile-section">
      <div class="section-header">
        <h2>成就徽章</h2>
        <el-button type="primary" plain size="small" @click="viewAllBadges">查看全部</el-button>
      </div>
      <div class="badges-grid">
        <div v-for="badge in userBadges" :key="badge.id" class="badge-item" :class="{ 'locked': !badge.unlocked }">
          <el-tooltip :content="badge.description" placement="top">
            <div class="badge-icon">
              <el-icon v-if="badge.unlocked" :size="24">
                <component :is="badge.icon"></component>
              </el-icon>
              <el-icon v-else><Lock /></el-icon>
            </div>
          </el-tooltip>
          <div class="badge-name">{{ badge.name }}</div>
        </div>
      </div>
    </div>

    <div class="profile-section">
      <div class="section-header">
        <h2>学习统计</h2>
        <el-button type="primary" plain size="small" @click="viewDetailedStats">详细数据</el-button>
      </div>
      <div class="stats-chart">
        <div class="chart-placeholder">
          <p>过去7天任务完成情况</p>
          <div class="bar-chart">
            <div v-for="(day, index) in weeklyStats" :key="index" class="chart-bar-container">
              <div class="chart-bar" :style="{ height: `${day.percentage}%` }"></div>
              <div class="chart-label">{{ day.label }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="profile-section">
      <div class="section-header">
        <h2>账号设置</h2>
      </div>
      <div class="settings-list">
        <div class="setting-item" @click="goToBinding">
          <div class="setting-info">
            <el-icon><Connection /></el-icon>
            <span>绑定管理</span>
          </div>
          <el-icon><ArrowRight /></el-icon>
        </div>
        <div class="setting-item" @click="changePassword">
          <div class="setting-info">
            <el-icon><Key /></el-icon>
            <span>修改密码</span>
          </div>
          <el-icon><ArrowRight /></el-icon>
        </div>
        <div class="setting-item" @click="notificationSettings">
          <div class="setting-info">
            <el-icon><Bell /></el-icon>
            <span>通知设置</span>
          </div>
          <el-icon><ArrowRight /></el-icon>
        </div>
        <div class="setting-item" @click="privacySettings">
          <div class="setting-info">
            <el-icon><Lock /></el-icon>
            <span>隐私设置</span>
          </div>
          <el-icon><ArrowRight /></el-icon>
        </div>
        <div class="setting-item danger" @click="logout">
          <div class="setting-info">
            <el-icon><SwitchButton /></el-icon>
            <span>退出登录</span>
          </div>
          <el-icon><ArrowRight /></el-icon>
        </div>
      </div>
    </div>

    <!-- 编辑个人信息对话框 -->
    <el-dialog
      v-model="profileDialogVisible"
      title="编辑个人信息"
      width="80%"
    >
      <el-form :model="profileForm" label-position="top">
        <el-form-item label="姓名">
          <el-input v-model="profileForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="手机号码">
          <el-input v-model="profileForm.phone" placeholder="请输入手机号码" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="profileDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveProfile">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../../stores/auth';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Edit,
  Key,
  Bell,
  Lock,
  SwitchButton,
  ArrowRight,
  Trophy,
  Star,
  Medal,
  Calendar,
  Histogram,
  Connection
} from '@element-plus/icons-vue';

// 常量定义
const STORAGE_KEY_PROFILE = 'userProfile';
const STORAGE_KEY_UNIVERSITY = 'targetUniversity';
const STORAGE_KEY_TASKS = 'myTasks';
const STORAGE_KEY_STONES = 'repairStones';
const DEFAULT_AVATAR = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png';

// 状态管理
const router = useRouter();
const authStore = useAuthStore();

// 用户信息
const userInfo = ref({
  name: '考研学子',
  email: authStore.userEmail || '<EMAIL>',
  phone: '',
  avatar: ''
});

// 目标院校信息
const university = ref(null);

// 用户统计数据
const userStats = ref({
  completedTasks: 0,
  totalPoints: 0,
  repairStones: 0,
  streak: 0
});

// 每周统计数据 - 模拟数据
const weeklyStats = ref([
  { label: '一', percentage: 30 },
  { label: '二', percentage: 50 },
  { label: '三', percentage: 20 },
  { label: '四', percentage: 80 },
  { label: '五', percentage: 40 },
  { label: '六', percentage: 60 },
  { label: '日', percentage: 70 }
]);

// 用户徽章 - 预定义徽章列表
const userBadges = ref([
  {
    id: 1,
    name: '初学者',
    description: '完成第一个任务',
    icon: 'Star',
    unlocked: true
  },
  {
    id: 2,
    name: '坚持不懈',
    description: '连续7天完成任务',
    icon: 'Calendar',
    unlocked: true
  },
  {
    id: 3,
    name: '数学达人',
    description: '完成10个数学任务',
    icon: 'Histogram',
    unlocked: false
  },
  {
    id: 4,
    name: '英语专家',
    description: '完成10个英语任务',
    icon: 'Medal',
    unlocked: false
  },
  {
    id: 5,
    name: '全能学霸',
    description: '所有科目任务完成率达到80%',
    icon: 'Trophy',
    unlocked: false
  }
]);

// 对话框状态
const profileDialogVisible = ref(false);
const profileForm = ref({
  name: '',
  phone: ''
});

// 方法
// 编辑个人信息
const editProfile = () => {
  profileForm.value = {
    name: userInfo.value.name,
    phone: userInfo.value.phone || ''
  };
  profileDialogVisible.value = true;
};

// 保存个人信息
const saveProfile = () => {
  try {
    // 更新用户信息
    userInfo.value.name = profileForm.value.name;
    userInfo.value.phone = profileForm.value.phone;
    
    // 保存到本地存储
    localStorage.setItem(STORAGE_KEY_PROFILE, JSON.stringify(userInfo.value));
    
    // 关闭对话框并提示
    profileDialogVisible.value = false;
    ElMessage.success('个人信息更新成功');
  } catch (error) {
    console.error('保存个人信息失败:', error);
    ElMessage.error('保存失败，请重试');
  }
};

// 功能开发中的提示
const showDevelopingFeature = (featureName) => {
  ElMessage.info(`${featureName}功能开发中`);
};

// 查看所有徽章
const viewAllBadges = () => showDevelopingFeature('查看所有徽章');

// 查看详细统计
const viewDetailedStats = () => showDevelopingFeature('详细统计');

// 修改密码
const changePassword = () => showDevelopingFeature('修改密码');

// 绑定管理
const goToBinding = () => {
  router.push('/student/binding');
};

// 通知设置
const notificationSettings = () => showDevelopingFeature('通知设置');

// 隐私设置
const privacySettings = () => showDevelopingFeature('隐私设置');

// 退出登录
const logout = () => {
  ElMessageBox.confirm(
    '确定要退出登录吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    authStore.logout();
    router.push('/login');
    ElMessage.success('已退出登录');
  }).catch(() => {
    // 取消操作
  });
};

// 数据加载函数
// 从本地存储加载用户信息
const loadUserProfile = () => {
  try {
    const savedProfile = localStorage.getItem(STORAGE_KEY_PROFILE);
    if (savedProfile) {
      const profile = JSON.parse(savedProfile);
      userInfo.value = { ...userInfo.value, ...profile };
    }
  } catch (error) {
    console.error('加载用户信息失败:', error);
  }
};

// 从本地存储加载目标院校信息
const loadUniversityData = () => {
  try {
    const savedUniversity = localStorage.getItem(STORAGE_KEY_UNIVERSITY);
    if (savedUniversity) {
      university.value = JSON.parse(savedUniversity);
    }
  } catch (error) {
    console.error('加载目标院校信息失败:', error);
  }
};

// 从本地存储加载任务数据并计算统计信息
const loadTasksAndCalculateStats = () => {
  try {
    const savedTasks = localStorage.getItem(STORAGE_KEY_TASKS);
    if (savedTasks) {
      const tasks = JSON.parse(savedTasks);
      
      // 计算已完成任务数
      userStats.value.completedTasks = tasks.filter(task => task.status === 'completed').length;
      
      // 计算总积分
      userStats.value.totalPoints = tasks
        .filter(task => task.status === 'completed')
        .reduce((sum, task) => sum + (task.points || 0), 0);
      
      // 计算徽章解锁状态（示例）
      if (userStats.value.completedTasks > 0) {
        // 解锁"初学者"徽章
        const beginnerBadge = userBadges.value.find(badge => badge.id === 1);
        if (beginnerBadge) beginnerBadge.unlocked = true;
      }
    }
  } catch (error) {
    console.error('加载任务数据失败:', error);
  }
};

// 从本地存储加载修复石数量
const loadRepairStones = () => {
  try {
    const savedStones = localStorage.getItem(STORAGE_KEY_STONES);
    if (savedStones !== null) {
      userStats.value.repairStones = parseInt(savedStones);
    }
  } catch (error) {
    console.error('加载修复石数量失败:', error);
  }
};

// 生命周期钩子
onMounted(() => {
  // 加载所有数据
  loadUserProfile();
  loadUniversityData();
  loadTasksAndCalculateStats();
  loadRepairStones();
  
  // 模拟连续学习天数（实际应用中应该从服务器获取或根据任务完成情况计算）
  userStats.value.streak = 5;
});
</script>

<style scoped>
.profile-page {
  padding: 16px;
  padding-bottom: 70px;
}

.profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.avatar-container {
  position: relative;
  margin-right: 16px;
}

.edit-avatar {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: #4263eb;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.user-info h1 {
  margin: 0;
  font-size: 22px;
  color: #303133;
}

.user-email {
  color: #909399;
  margin: 4px 0 0 0;
}

.stats-card {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #4263eb;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.profile-section {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h2 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.info-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.info-label {
  color: #909399;
}

.info-value {
  color: #303133;
  font-weight: 500;
}

.badges-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
}

.badge-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.badge-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #ecf5ff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4263eb;
}

.badge-item.locked .badge-icon {
  background-color: #f5f7fa;
  color: #c0c4cc;
}

.badge-name {
  font-size: 12px;
  color: #606266;
  text-align: center;
}

.stats-chart {
  height: 200px;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-placeholder p {
  margin: 0 0 16px 0;
  color: #909399;
  text-align: center;
}

.bar-chart {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding-bottom: 24px;
}

.chart-bar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 12%;
}

.chart-bar {
  width: 100%;
  background-color: #4263eb;
  border-radius: 4px 4px 0 0;
}

.chart-label {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.settings-list {
  display: flex;
  flex-direction: column;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #303133;
}

.setting-item.danger .setting-info {
  color: #f56c6c;
}

@media (max-width: 768px) {
  .stats-card {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .badges-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>