// {{ AURA-X: Add - 创建CloudBase实时同步服务. Approval: 寸止(ID:1734682800). }}
// CloudBase实时同步服务
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

class RealtimeService {
  constructor() {
    this.listeners = new Map() // 存储监听器
    this.isConnected = ref(false)
    this.connectionStatus = ref('disconnected') // disconnected, connecting, connected, error
    this.subscriptions = reactive({}) // 存储订阅状态
    this.retryCount = 0
    this.maxRetries = 3
    this.retryDelay = 2000
  }

  /**
   * 初始化实时连接
   * @param {Object} config - CloudBase配置
   */
  async init(config) {
    try {
      this.connectionStatus.value = 'connecting'
      
      // 这里应该初始化CloudBase实时连接
      // 由于CloudBase实时功能需要在前端直接使用CloudBase SDK
      // 我们先创建一个模拟的实时连接，后续可以替换为真实的CloudBase实时监听
      
      console.log('正在初始化CloudBase实时连接...', config)
      
      // 模拟连接成功
      setTimeout(() => {
        this.isConnected.value = true
        this.connectionStatus.value = 'connected'
        this.retryCount = 0
        console.log('CloudBase实时连接已建立')
      }, 1000)
      
    } catch (error) {
      console.error('CloudBase实时连接初始化失败:', error)
      this.connectionStatus.value = 'error'
      this.handleConnectionError(error)
    }
  }

  /**
   * 订阅集合变更
   * @param {string} collection - 集合名称
   * @param {Object} query - 查询条件
   * @param {Function} callback - 变更回调函数
   */
  subscribe(collection, query = {}, callback) {
    const subscriptionKey = `${collection}_${JSON.stringify(query)}`
    
    if (this.subscriptions[subscriptionKey]) {
      console.warn(`已存在对 ${collection} 的订阅`)
      return subscriptionKey
    }

    try {
      // 创建订阅
      const subscription = {
        collection,
        query,
        callback,
        active: true,
        createdAt: new Date()
      }

      this.subscriptions[subscriptionKey] = subscription
      
      // 这里应该是真实的CloudBase实时监听代码
      // 示例：
      // const listener = db.collection(collection).where(query).watch({
      //   onChange: (snapshot) => {
      //     callback(snapshot.docs, snapshot.docChanges)
      //   },
      //   onError: (error) => {
      //     console.error('实时监听错误:', error)
      //     this.handleSubscriptionError(subscriptionKey, error)
      //   }
      // })
      
      // 启用基于API的实时数据同步
      this.startApiBasedSync(subscriptionKey, callback)
      
      console.log(`已订阅集合 ${collection} 的实时更新`)
      return subscriptionKey
      
    } catch (error) {
      console.error(`订阅 ${collection} 失败:`, error)
      throw error
    }
  }

  /**
   * 取消订阅
   * @param {string} subscriptionKey - 订阅键
   */
  unsubscribe(subscriptionKey) {
    const subscription = this.subscriptions[subscriptionKey]
    if (!subscription) {
      console.warn(`未找到订阅: ${subscriptionKey}`)
      return
    }

    try {
      // 标记为非活跃
      subscription.active = false
      
      // 这里应该调用CloudBase的取消监听方法
      // 示例：listener.close()
      
      delete this.subscriptions[subscriptionKey]
      console.log(`已取消订阅: ${subscriptionKey}`)
      
    } catch (error) {
      console.error(`取消订阅失败: ${subscriptionKey}`, error)
    }
  }

  /**
   * 取消所有订阅
   */
  unsubscribeAll() {
    const keys = Object.keys(this.subscriptions)
    keys.forEach(key => this.unsubscribe(key))
    console.log('已取消所有实时订阅')
  }

  /**
   * 处理连接错误
   */
  handleConnectionError(error) {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++
      console.log(`连接失败，${this.retryDelay}ms后进行第${this.retryCount}次重试...`)
      
      setTimeout(() => {
        this.init()
      }, this.retryDelay)
      
      // 指数退避
      this.retryDelay *= 2
    } else {
      console.error('达到最大重试次数，停止重连')
      ElMessage.error('实时连接失败，请刷新页面重试')
    }
  }

  /**
   * 处理订阅错误
   */
  handleSubscriptionError(subscriptionKey, error) {
    console.error(`订阅 ${subscriptionKey} 发生错误:`, error)
    
    const subscription = this.subscriptions[subscriptionKey]
    if (subscription && subscription.active) {
      // 尝试重新订阅
      setTimeout(() => {
        this.subscribe(subscription.collection, subscription.query, subscription.callback)
      }, 5000)
    }
  }

  /**
   * 基于API的实时数据同步
   * 定期检查API数据变更并触发回调
   */
  startApiBasedSync(subscriptionKey, callback) {
    const subscription = this.subscriptions[subscriptionKey]
    if (!subscription) return

    // 存储上次数据的哈希值，用于检测变更
    let lastDataHash = null

    // 轮询检查数据变更
    const checkForUpdates = async () => {
      if (!this.subscriptions[subscriptionKey]?.active) {
        return
      }

      try {
        // 根据集合类型调用相应的API
        let apiResponse = null
        const { collection } = subscription
        const token = localStorage.getItem('token')

        if (!token) return

        if (collection === 'tasks') {
          // 获取我的任务数据
          const response = await fetch('/api/tasks/my-tasks', {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          })
          if (response.ok) {
            apiResponse = await response.json()
          }
        } else if (collection === 'task-completions') {
          // 获取任务完成数据
          const response = await fetch('/api/task-completions', {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          })
          if (response.ok) {
            apiResponse = await response.json()
          }
        } else if (collection === 'bindings') {
          // 获取绑定信息和统计数据
          const response = await fetch('/api/bindings/my-binding', {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          })
          if (response.ok) {
            apiResponse = await response.json()
          }
        } else if (collection === 'student-stats') {
          // 获取学生统计数据
          const response = await fetch('/api/bindings/my-binding', {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          })
          if (response.ok) {
            apiResponse = await response.json()
          }
        }

        if (apiResponse && apiResponse.data) {
          // 计算数据哈希值
          const currentDataHash = this.hashData(apiResponse.data)

          // 如果数据发生变更，触发回调
          if (lastDataHash && lastDataHash !== currentDataHash) {
            console.log(`检测到 ${collection} 数据变更，触发同步`)
            callback(apiResponse.data, 'update')

            // 触发全局事件
            window.dispatchEvent(new CustomEvent(`${collection}-updated`, {
              detail: apiResponse.data
            }))
          }

          lastDataHash = currentDataHash
        }

      } catch (error) {
        console.error(`检查 ${subscription.collection} 数据变更失败:`, error)
      }
    }

    // 立即执行一次
    checkForUpdates()

    // 每5秒检查一次数据变更
    const interval = setInterval(checkForUpdates, 5000)

    // 存储interval以便后续清理
    this.subscriptions[subscriptionKey].interval = interval
  }

  /**
   * 计算数据哈希值
   * @param {any} data - 数据
   * @returns {string} 哈希值
   */
  hashData(data) {
    try {
      return btoa(JSON.stringify(data)).slice(0, 16)
    } catch (error) {
      return String(Date.now()).slice(-8)
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected.value,
      status: this.connectionStatus.value,
      subscriptions: Object.keys(this.subscriptions).length
    }
  }

  /**
   * 销毁服务
   */
  destroy() {
    this.unsubscribeAll()
    this.isConnected.value = false
    this.connectionStatus.value = 'disconnected'
    console.log('实时同步服务已销毁')
  }
}

// 创建单例实例
const realtimeService = new RealtimeService()

export default realtimeService
