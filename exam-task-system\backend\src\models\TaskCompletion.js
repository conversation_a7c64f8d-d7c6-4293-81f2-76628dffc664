const db = require('../config/database');
const { createDocument, getDocumentById, updateDocument, deleteDocument, queryDocuments } = require('../utils/modelHelper');

// 集合名称
const COLLECTION = 'task_completions';

/**
 * 创建任务完成记录
 * @param {Object} completionData - 完成记录数据
 * @returns {Promise} - 返回创建的完成记录
 */
const createTaskCompletion = async (completionData) => {
  try {
    return await createDocument(db, COLLECTION, {
      taskId: completionData.taskId,
      studentId: completionData.studentId,
      completionDate: completionData.completionDate || new Date(),
      evidence: completionData.evidence || '',
      status: completionData.status || 'pending_review',
      feedback: completionData.feedback || '',
      reviewerId: completionData.reviewerId || null,
      reviewDate: completionData.reviewDate || null,
      pointsAwarded: completionData.pointsAwarded || null
    });
  } catch (error) {
    console.error('创建任务完成记录失败:', error);
    throw error;
  }
};

/**
 * 根据ID获取任务完成记录
 * @param {String} id - 完成记录ID
 * @returns {Promise} - 返回完成记录数据
 */
const getTaskCompletionById = async (id) => {
  try {
    return await getDocumentById(db, COLLECTION, id);
  } catch (error) {
    console.error('获取任务完成记录失败:', error);
    throw error;
  }
};

/**
 * 更新任务完成记录
 * @param {String} id - 完成记录ID
 * @param {Object} completionData - 要更新的完成记录数据
 * @returns {Promise} - 返回更新后的完成记录
 */
const updateTaskCompletion = async (id, completionData) => {
  try {
    return await updateDocument(db, COLLECTION, id, completionData);
  } catch (error) {
    console.error('更新任务完成记录失败:', error);
    throw error;
  }
};

/**
 * 获取任务的完成记录
 * @param {String} taskId - 任务ID
 * @returns {Promise} - 返回完成记录列表
 */
const getTaskCompletionsByTaskId = async (taskId) => {
  try {
    return await queryDocuments(db, COLLECTION, { taskId });
  } catch (error) {
    console.error('获取任务完成记录列表失败:', error);
    throw error;
  }
};

/**
 * 获取学生的任务完成记录
 * @param {String} studentId - 学生ID
 * @param {Object} options - 查询选项
 * @returns {Promise} - 返回完成记录列表
 */
const getStudentTaskCompletions = async (studentId, options = {}) => {
  try {
    return await queryDocuments(db, COLLECTION, { studentId }, options);
  } catch (error) {
    console.error('获取学生任务完成记录失败:', error);
    throw error;
  }
};

/**
 * 获取待审核的任务完成记录
 * @param {Object} options - 查询选项
 * @returns {Promise} - 返回完成记录列表
 */
const getPendingReviewTaskCompletions = async (options = {}) => {
  try {
    return await queryDocuments(db, COLLECTION, { status: 'pending_review' }, options);
  } catch (error) {
    console.error('获取待审核任务完成记录失败:', error);
    throw error;
  }
};

/**
 * 审核任务完成记录
 * @param {String} id - 完成记录ID
 * @param {String} status - 审核状态
 * @param {String} feedback - 反馈
 * @param {String} reviewerId - 审核者ID
 * @param {Number} pointsAwarded - 奖励积分
 * @returns {Promise} - 返回更新后的完成记录
 */
const reviewTaskCompletion = async (id, status, feedback, reviewerId, pointsAwarded) => {
  try {
    return await updateTaskCompletion(id, {
      status,
      feedback,
      reviewerId,
      reviewDate: new Date(),
      pointsAwarded
    });
  } catch (error) {
    console.error('审核任务完成记录失败:', error);
    throw error;
  }
};

/**
 * 检查任务是否已经有完成记录
 * @param {String} taskId - 任务ID
 * @param {String} studentId - 学生ID
 * @returns {Promise} - 返回是否存在完成记录
 */
const hasTaskCompletion = async (taskId, studentId) => {
  try {
    const completions = await queryDocuments(db, COLLECTION, {
      taskId,
      studentId,
      status: { $ne: 'rejected' }
    });
    return completions.length > 0;
  } catch (error) {
    console.error('检查任务完成记录失败:', error);
    throw error;
  }
};

/**
 * 根据学生ID获取任务完成记录
 * @param {String} studentId - 学生ID
 * @returns {Promise} - 返回学生的所有任务完成记录
 */
const getCompletionsByStudent = async (studentId) => {
  try {
    return await queryDocuments(db, COLLECTION, { studentId });
  } catch (error) {
    console.error('根据学生ID获取任务完成记录失败:', error);
    throw error;
  }
};

/**
 * 删除学生的所有任务完成记录
 * @param {String} studentId - 学生ID
 * @returns {Promise} - 返回删除的记录数量
 */
const deleteCompletionsByStudent = async (studentId) => {
  try {
    const completions = await getCompletionsByStudent(studentId);
    let deletedCount = 0;

    for (const completion of completions) {
      await deleteDocument(db, COLLECTION, completion._id);
      deletedCount++;
    }

    console.log(`删除学生 ${studentId} 的 ${deletedCount} 个任务完成记录`);
    return deletedCount;
  } catch (error) {
    console.error('删除学生任务完成记录失败:', error);
    throw error;
  }
};

// {{ AURA-X: Add - 添加获取所有完成记录和删除方法. Approval: 寸止(ID:1734683400). }}
// 获取所有任务完成记录
const getAllTaskCompletions = async () => {
  try {
    return await queryDocuments(db, COLLECTION, {});
  } catch (error) {
    console.error('获取所有任务完成记录失败:', error);
    throw error;
  }
};

// 删除任务完成记录
const deleteTaskCompletion = async (id) => {
  try {
    return await deleteDocument(db, COLLECTION, id);
  } catch (error) {
    console.error('删除任务完成记录失败:', error);
    throw error;
  }
};

module.exports = {
  createTaskCompletion,
  getTaskCompletionById,
  updateTaskCompletion,
  getTaskCompletionsByTaskId,
  getStudentTaskCompletions,
  getPendingReviewTaskCompletions,
  reviewTaskCompletion,
  hasTaskCompletion,
  getCompletionsByStudent,
  deleteCompletionsByStudent,
  getAllTaskCompletions,
  deleteTaskCompletion
};