const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const helmet = require('helmet');
const compression = require('compression');
const dotenv = require('dotenv');
const { initCloudBase, testCloudBaseConnection } = require('./config/cloudbase');
const apiRoutes = require('./routes/api');

// 加载环境变量
dotenv.config();

// 初始化 CloudBase
initCloudBase();

// 测试 CloudBase 连接
setTimeout(async () => {
  await testCloudBaseConnection();
}, 2000); // 延迟2秒测试，确保初始化完成

// 创建 Express 应用
const app = express();

// 中间件
app.use(helmet()); // 安全头
app.use(compression()); // 压缩响应
// 配置 CORS
app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? ['https://your-project.vercel.app'] // 生产环境域名
    : ['http://localhost:5173', 'http://localhost:5174'], // 开发环境端口
  credentials: true, // 允许携带凭证
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
})); // 跨域资源共享
app.use(morgan('dev')); // 日志
app.use(express.json()); // 解析 JSON 请求体
app.use(express.urlencoded({ extended: true })); // 解析 URL 编码的请求体

// API 路由
app.use('/api', apiRoutes);

// 根路由
app.get('/', (req, res) => {
  res.json({
    message: '考研任务系统 API',
    version: '1.0.0',
    status: 'running'
  });
});

// 404 处理
app.use((req, res, next) => {
  res.status(404).json({
    message: '未找到请求的资源'
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error(err.stack);
  
  res.status(err.status || 500).json({
    message: err.message || '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? err : {}
  });
});

module.exports = app;