# 🚀 Vercel部署指南 - 免费云端后端

## 🎯 为什么选择Vercel

- ✅ **完全免费** - 个人项目永久免费
- ✅ **零维护** - 自动部署和扩容  
- ✅ **全球CDN** - 访问速度快
- ✅ **自动HTTPS** - 免费SSL证书
- ✅ **简单部署** - 几分钟完成

## 📋 部署步骤

### 步骤1: 准备代码仓库

1. **创建GitHub仓库**
   ```bash
   # 如果还没有Git仓库，初始化一个
   git init
   git add .
   git commit -m "Initial commit"
   
   # 推送到GitHub
   git remote add origin https://github.com/yourusername/exam-task-system.git
   git push -u origin main
   ```

### 步骤2: 配置Vercel部署文件

1. **创建vercel.json配置文件**
   ```json
   {
     "version": 2,
     "builds": [
       {
         "src": "backend/src/server.js",
         "use": "@vercel/node"
       },
       {
         "src": "package.json",
         "use": "@vercel/static-build",
         "config": {
           "distDir": "dist"
         }
       }
     ],
     "routes": [
       {
         "src": "/api/(.*)",
         "dest": "backend/src/server.js"
       },
       {
         "src": "/(.*)",
         "dest": "/index.html"
       }
     ],
     "env": {
       "NODE_ENV": "production"
     }
   }
   ```

2. **修改后端入口文件**
   在 `backend/src/server.js` 中添加Vercel兼容性：
   ```javascript
   // 在文件末尾添加
   module.exports = app;
   ```

### 步骤3: 配置环境变量

1. **创建.env.example文件**
   ```env
   # CloudBase配置
   CLOUDBASE_ENV_ID=your_environment_id
   CLOUDBASE_SECRET_ID=your_secret_id  
   CLOUDBASE_SECRET_KEY=your_secret_key
   
   # JWT配置
   JWT_SECRET=your_jwt_secret_key
   JWT_EXPIRES_IN=7d
   
   # 服务器配置
   NODE_ENV=production
   ```

### 步骤4: 部署到Vercel

1. **注册Vercel账号**
   - 访问 https://vercel.com
   - 使用GitHub账号登录

2. **导入项目**
   - 点击"New Project"
   - 选择你的GitHub仓库
   - 点击"Import"

3. **配置环境变量**
   - 在项目设置中添加环境变量
   - 复制.env文件中的所有变量

4. **部署**
   - 点击"Deploy"
   - 等待部署完成

### 步骤5: 配置前端API地址

1. **更新前端配置**
   在 `src/config/api.js` 中：
   ```javascript
   const API_BASE_URL = process.env.NODE_ENV === 'production' 
     ? 'https://your-project.vercel.app/api'
     : 'http://localhost:5000/api';
   ```

## 🔧 项目结构调整

### 调整后端结构

1. **创建api目录**
   ```
   exam-task-system/
   ├── api/
   │   └── index.js (Vercel入口文件)
   ├── backend/
   │   └── src/
   ├── src/ (前端代码)
   └── vercel.json
   ```

2. **创建api/index.js**
   ```javascript
   const app = require('../backend/src/app');
   
   module.exports = app;
   ```

### 修改package.json

```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "vercel-build": "vite build"
  }
}
```

## 🎯 部署后测试

1. **获取部署URL**
   - Vercel会提供一个免费域名
   - 例如：`https://your-project.vercel.app`

2. **测试API**
   ```bash
   curl https://your-project.vercel.app/api/auth/test
   ```

3. **测试前端**
   - 访问 `https://your-project.vercel.app`
   - 测试登录和绑定功能

## 🔄 自动部署

**配置完成后**：
- 每次推送代码到GitHub
- Vercel自动重新部署
- 无需手动操作

## 💡 优化建议

1. **自定义域名**
   - 在Vercel控制台绑定自己的域名
   - 自动配置SSL证书

2. **性能监控**
   - Vercel提供详细的性能分析
   - 可以监控API响应时间

3. **环境分离**
   - 可以创建预览环境
   - 生产环境和开发环境分离

## 🆘 常见问题

**Q: 数据库连接问题？**
A: 确保CloudBase环境变量正确配置

**Q: API路由不工作？**  
A: 检查vercel.json中的路由配置

**Q: 构建失败？**
A: 检查package.json中的构建脚本

## 🎉 完成

部署完成后，你将获得：
- 免费的全球CDN
- 自动HTTPS
- 无限带宽
- 自动部署
- 零维护成本

现在可以将链接分享给用户使用了！
