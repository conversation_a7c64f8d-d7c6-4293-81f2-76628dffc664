<template>
  <div class="dashboard-container">
    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="user-info">
        <div class="avatar">{{ userInitial }}</div>
        <div class="user-details">
          <div class="username">{{ authStore.user?.username || '监督者' }}</div>
          <div class="role">监督者</div>
        </div>
      </div>
      
      <nav class="nav-menu">
        <router-link to="/supervisor" class="nav-item" :class="{ active: $route.path === '/supervisor' }">
          <span class="nav-icon">🏠</span>
          <span>首页</span>
        </router-link>
        
        <router-link to="/supervisor/binding" class="nav-item" :class="{ active: $route.path === '/supervisor/binding' }">
          <span class="nav-icon">🔗</span>
          <span>绑定管理</span>
        </router-link>
        
        <router-link to="/supervisor/tasks" class="nav-item" :class="{ active: $route.path === '/supervisor/tasks' }">
          <span class="nav-icon">📋</span>
          <span>任务管理</span>
        </router-link>
        
        <router-link to="/supervisor/create-task" class="nav-item" :class="{ active: $route.path === '/supervisor/create-task' }">
          <span class="nav-icon">➕</span>
          <span>创建任务</span>
        </router-link>
        
        <router-link to="/supervisor/statistics" class="nav-item" :class="{ active: $route.path === '/supervisor/statistics' }">
          <span class="nav-icon">📊</span>
          <span>数据统计</span>
        </router-link>
        
        <router-link to="/supervisor/rewards" class="nav-item" :class="{ active: $route.path === '/supervisor/rewards' }">
          <span class="nav-icon">🏆</span>
          <span>奖励管理</span>
        </router-link>
      </nav>
      
      <div class="logout-section">
        <button @click="logout" class="logout-btn">
          <span>🚪</span>
          退出登录
        </button>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 顶部导航 -->
      <header class="header">
        <h1 class="page-title">{{ pageTitle }}</h1>
      </header>
      
      <!-- 内容区域 -->
      <main class="content">
        <!-- 根据路由显示不同内容 -->
        <router-view v-if="$route.path !== '/supervisor'" :key="$route.fullPath" />
        
        <!-- 默认首页内容 -->
        <div v-else class="home-content">
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon">👥</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.totalStudents }}</div>
                <div class="stat-label">绑定学生</div>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">📋</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.totalTasks }}</div>
                <div class="stat-label">发布任务</div>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">✅</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.completedTasks }}</div>
                <div class="stat-label">完成任务</div>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">🏆</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.totalRewards }}</div>
                <div class="stat-label">发放奖励</div>
              </div>
            </div>
          </div>
          
          <div class="quick-actions">
            <h2>快速操作</h2>
            <div class="action-buttons">
              <router-link to="/supervisor/binding" class="action-btn">
                <span class="action-icon">🔗</span>
                <span>管理绑定</span>
              </router-link>
              
              <router-link to="/supervisor/create-task" class="action-btn">
                <span class="action-icon">➕</span>
                <span>创建任务</span>
              </router-link>
              
              <router-link to="/supervisor/statistics" class="action-btn">
                <span class="action-icon">📊</span>
                <span>查看统计</span>
              </router-link>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { bindingAPI } from '@/api/binding';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();

// 响应式数据
const stats = ref({
  totalStudents: 0,
  totalTasks: 0,
  completedTasks: 0,
  totalRewards: 0
});

// 计算属性
const userInitial = computed(() => {
  return authStore.user?.username?.charAt(0)?.toUpperCase() || 'S';
});

const pageTitle = computed(() => {
  const titles = {
    '/supervisor': '监督者首页',
    '/supervisor/binding': '绑定管理',
    '/supervisor/tasks': '任务管理',
    '/supervisor/create-task': '创建任务',
    '/supervisor/statistics': '数据统计',
    '/supervisor/rewards': '奖励管理'
  };
  return titles[route.path] || '监督者首页';
});

// 方法
const logout = () => {
  authStore.logout();
  router.push('/login');
};

const loadStats = async () => {
  try {
    // 获取真实的统计数据
    const response = await bindingAPI.getStudentList({
      supervisor_id: authStore.user.id
    });

    if (response && response.data) {
      const students = response.data;
      const totalStudents = students.length;

      // 计算总任务数和完成任务数
      let totalTasks = 0;
      let completedTasks = 0;

      students.forEach(student => {
        if (student.task_stats) {
          totalTasks += student.task_stats.accepted_tasks || 0;
          completedTasks += student.task_stats.completed_tasks || 0;
        }
      });

      stats.value = {
        totalStudents,
        totalTasks,
        completedTasks,
        totalRewards: 0 // 暂时设为0，等奖励功能实现
      };
    } else {
      // 如果没有数据，使用默认值
      stats.value = {
        totalStudents: 0,
        totalTasks: 0,
        completedTasks: 0,
        totalRewards: 0
      };
    }
  } catch (error) {
    console.error('加载统计数据失败:', error);
    // 错误时使用默认值
    stats.value = {
      totalStudents: 0,
      totalTasks: 0,
      completedTasks: 0,
      totalRewards: 0
    };
  }
};

// 生命周期
onMounted(() => {
  loadStats();
});
</script>

<style>
/* 全局样式，不使用scoped */
.dashboard-container {
  display: flex !important;
  min-height: 100vh !important;
  background-color: #f5f5f5 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.sidebar {
  width: 250px !important;
  background: white !important;
  border-right: 1px solid #e5e7eb !important;
  display: flex !important;
  flex-direction: column !important;
  position: fixed !important;
  height: 100vh !important;
  left: 0 !important;
  top: 0 !important;
  z-index: 1000 !important;
}

.user-info {
  padding: 20px !important;
  border-bottom: 1px solid #e5e7eb !important;
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
}

.avatar {
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  background: #3b82f6 !important;
  color: white !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: bold !important;
}

.user-details {
  flex: 1 !important;
}

.username {
  font-weight: 600 !important;
  color: #1f2937 !important;
  font-size: 14px !important;
}

.role {
  font-size: 12px !important;
  color: #6b7280 !important;
}

.nav-menu {
  flex: 1 !important;
  padding: 20px 0 !important;
}

.nav-item {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  padding: 12px 20px !important;
  color: #6b7280 !important;
  text-decoration: none !important;
  transition: all 0.2s !important;
  border: none !important;
}

.nav-item:hover {
  background: #f3f4f6 !important;
  color: #3b82f6 !important;
}

.nav-item.active {
  background: #eff6ff !important;
  color: #3b82f6 !important;
  border-right: 3px solid #3b82f6 !important;
}

.nav-icon {
  font-size: 18px !important;
}

.logout-section {
  padding: 20px !important;
  border-top: 1px solid #e5e7eb !important;
}

.logout-btn {
  width: 100% !important;
  padding: 10px !important;
  background: #fee2e2 !important;
  color: #dc2626 !important;
  border: 1px solid #fecaca !important;
  border-radius: 6px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
  cursor: pointer !important;
  transition: all 0.2s !important;
}

.logout-btn:hover {
  background: #fecaca !important;
}

.main-content {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  margin-left: 250px !important;
}

.header {
  height: 60px !important;
  background: white !important;
  border-bottom: 1px solid #e5e7eb !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: 0 24px !important;
}

.page-title {
  font-size: 20px !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
  margin: 0 !important;
}

.content {
  flex: 1 !important;
  padding: 24px !important;
  overflow-y: auto !important;
  background: #f5f5f5 !important;
}

.home-content {
  max-width: 1200px !important;
}

.stats-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
  gap: 20px !important;
  margin-bottom: 30px !important;
}

.stat-card {
  background: white !important;
  padding: 24px !important;
  border-radius: 8px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  display: flex !important;
  align-items: center !important;
  gap: 16px !important;
}

.stat-icon {
  font-size: 32px !important;
}

.stat-number {
  font-size: 24px !important;
  font-weight: bold !important;
  color: #1f2937 !important;
}

.stat-label {
  font-size: 14px !important;
  color: #6b7280 !important;
}

.quick-actions {
  background: white !important;
  padding: 24px !important;
  border-radius: 8px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.quick-actions h2 {
  margin: 0 0 20px 0 !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
}

.action-buttons {
  display: flex !important;
  gap: 16px !important;
  flex-wrap: wrap !important;
}

.action-btn {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 12px 20px !important;
  background: #f3f4f6 !important;
  color: #374151 !important;
  text-decoration: none !important;
  border-radius: 6px !important;
  transition: all 0.2s !important;
}

.action-btn:hover {
  background: #e5e7eb !important;
  transform: translateY(-1px) !important;
}

.action-icon {
  font-size: 18px !important;
}
</style>
