const express = require('express');
const router = express.Router();
const {
  generateBindCode,
  confirmBinding,
  getBindingInfo,
  unbind,
  getStudentList,
  getMyBinding
} = require('../controllers/bindingController');
const { authenticate } = require('../middleware/auth');

// 所有路由都需要认证
router.use(authenticate);

// 生成绑定码（监督者）
router.post('/generate', generateBindCode);

// 确认绑定（学生输入绑定码）
router.post('/confirm', confirmBinding);

// 获取绑定信息
router.get('/info', getBindingInfo);

// 获取我的绑定信息（从token中获取用户信息）
router.get('/my', getMyBinding);

// 获取监督者的学生列表
router.get('/students', getStudentList);

// 解除绑定
router.post('/unbind', unbind);

module.exports = router;
