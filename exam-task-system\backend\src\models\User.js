const db = require('../config/database');
const bcrypt = require('bcrypt');
const { createDocument, getDocumentById, updateDocument, queryDocuments } = require('../utils/modelHelper');

// 集合名称
const COLLECTION = 'users';

/**
 * 创建新用户
 * @param {Object} userData - 用户数据
 * @returns {Promise} - 返回创建的用户
 */
const createUser = async (userData) => {
  try {
    // 检查用户名是否已存在
    const existingUser = await getUserByUsername(userData.username);
    if (existingUser) {
      throw new Error('用户名已存在');
    }
    
    // 检查邮箱是否已存在
    const existingEmail = await getUserByEmail(userData.email);
    if (existingEmail) {
      throw new Error('邮箱已被使用');
    }
    
    // 加密密码
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(userData.password, salt);
    
    // 创建用户
    const user = await createDocument(db, COLLECTION, {
      username: userData.username,
      password: hashedPassword,
      name: userData.name,
      email: userData.email,
      role: userData.role || 'student',
      points: userData.points || 0
    });
    
    // 删除密码后返回
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  } catch (error) {
    console.error('创建用户失败:', error);
    throw error;
  }
};

/**
 * 根据ID获取用户
 * @param {String} id - 用户ID
 * @returns {Promise} - 返回用户数据
 */
const getUserById = async (id) => {
  try {
    const user = await getDocumentById(db, COLLECTION, id);
    if (!user) return null;
    
    // 删除密码后返回
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  } catch (error) {
    console.error('获取用户失败:', error);
    throw error;
  }
};

/**
 * 根据用户名获取用户
 * @param {String} username - 用户名
 * @returns {Promise} - 返回用户数据
 */
const getUserByUsername = async (username) => {
  try {
    const users = await queryDocuments(db, COLLECTION, { username });
    if (users.length === 0) return null;
    return users[0];
  } catch (error) {
    console.error('根据用户名获取用户失败:', error);
    throw error;
  }
};

/**
 * 根据邮箱获取用户
 * @param {String} email - 邮箱
 * @returns {Promise} - 返回用户数据
 */
const getUserByEmail = async (email) => {
  try {
    const users = await queryDocuments(db, COLLECTION, { email });
    if (users.length === 0) return null;
    return users[0];
  } catch (error) {
    console.error('根据邮箱获取用户失败:', error);
    throw error;
  }
};

/**
 * 更新用户信息
 * @param {String} id - 用户ID
 * @param {Object} userData - 要更新的用户数据
 * @returns {Promise} - 返回更新后的用户
 */
const updateUser = async (id, userData) => {
  try {
    // 如果要更新密码，先加密
    if (userData.password) {
      const salt = await bcrypt.genSalt(10);
      userData.password = await bcrypt.hash(userData.password, salt);
    }
    
    const updatedUser = await updateDocument(db, COLLECTION, id, userData);
    
    // 删除密码后返回
    const { password, ...userWithoutPassword } = updatedUser;
    return userWithoutPassword;
  } catch (error) {
    console.error('更新用户失败:', error);
    throw error;
  }
};

/**
 * 获取所有学生
 * @returns {Promise} - 返回学生列表
 */
const getAllStudents = async () => {
  try {
    const students = await queryDocuments(db, COLLECTION, { role: 'student' });
    
    // 删除密码后返回
    return students.map(student => {
      const { password, ...studentWithoutPassword } = student;
      return studentWithoutPassword;
    });
  } catch (error) {
    console.error('获取所有学生失败:', error);
    throw error;
  }
};

/**
 * 验证用户密码
 * @param {String} username - 用户名
 * @param {String} password - 密码
 * @returns {Promise} - 返回验证结果和用户数据
 */
const validateUser = async (username, password) => {
  try {
    const user = await getUserByUsername(username);
    if (!user) return { isValid: false, user: null };
    
    const isValid = await bcrypt.compare(password, user.password);
    
    if (!isValid) return { isValid: false, user: null };
    
    // 删除密码后返回
    const { password: userPassword, ...userWithoutPassword } = user;
    return { isValid: true, user: userWithoutPassword };
  } catch (error) {
    console.error('验证用户失败:', error);
    throw error;
  }
};

/**
 * 更新用户积分
 * @param {String} id - 用户ID
 * @param {Number} points - 积分变动（正数为增加，负数为减少）
 * @returns {Promise} - 返回更新后的用户
 */
const updateUserPoints = async (id, points) => {
  try {
    const user = await getDocumentById(db, COLLECTION, id);
    if (!user) throw new Error('用户不存在');
    
    const newPoints = user.points + points;
    if (newPoints < 0) throw new Error('积分不足');
    
    return await updateUser(id, { points: newPoints });
  } catch (error) {
    console.error('更新用户积分失败:', error);
    throw error;
  }
};

// {{ AURA-X: Add - 添加删除用户方法. Approval: 寸止(ID:1734683400). }}
// 删除用户
const deleteUser = async (id) => {
  try {
    return await deleteDocument(db, COLLECTION, id);
  } catch (error) {
    console.error('删除用户失败:', error);
    throw error;
  }
};

// 获取所有用户
const getAllUsers = async () => {
  try {
    return await queryDocuments(db, COLLECTION, {});
  } catch (error) {
    console.error('获取所有用户失败:', error);
    throw error;
  }
};

module.exports = {
  createUser,
  getUserById,
  getUserByUsername,
  getUserByEmail,
  updateUser,
  getAllStudents,
  validateUser,
  updateUserPoints,
  deleteUser,
  getAllUsers
};