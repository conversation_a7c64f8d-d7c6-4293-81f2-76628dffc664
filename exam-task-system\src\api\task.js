import api from './index'

export const taskAPI = {
  // 创建任务（监督者）
  createTask: (data) => api.post('/tasks', data),

  // 获取任务列表
  getTaskList: (params) => api.get('/tasks', { params }),

  // 获取任务详情
  getTaskDetail: (taskId) => api.get(`/tasks/${taskId}`),

  // 更新任务（监督者）
  updateTask: (taskId, data) => api.put(`/tasks/${taskId}`, data),

  // 删除任务（监督者）
  deleteTask: (taskId) => api.delete(`/tasks/${taskId}`),

  // 提交任务完成（学生）
  submitTaskCompletion: (taskId, data) => api.post(`/tasks/${taskId}/complete`, data),

  // 审核任务完成（监督者）
  reviewTaskCompletion: (completionId, data) => api.put(`/task-completions/${completionId}/review`, data),

  // 获取我的任务
  getMyTasks: (params) => api.get('/tasks/my-tasks', { params }),

  // 获取固定任务列表（任务广场）
  getFixedTasks: () => api.get('/tasks/fixed'),

  // 接受固定任务
  acceptFixedTask: (taskData) => api.post('/tasks/fixed/accept', { taskData }),

  // 获取学生任务列表（监督者用）
  getStudentTasks: (studentId) => api.get(`/tasks/student/${studentId}`)
}
