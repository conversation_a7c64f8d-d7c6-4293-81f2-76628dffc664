"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildCommonOpenApiUrlWithPath = exports.buildUrl = void 0;
/* eslint-disable complexity */
function buildUrl(options) {
    const endpoint = `https://${options.envId}.api.tcloudbasegateway.com/v1/cloudrun/${options.name}`;
    const path = options.path.startsWith('/') ? options.path : `/${options.path}`;
    return `${endpoint}${path}`;
}
exports.buildUrl = buildUrl;
function buildCommonOpenApiUrlWithPath(options) {
    return `${options.protocol || 'https'}://${options.envId}.api.tcloudbasegateway.com${options.path}`;
}
exports.buildCommonOpenApiUrlWithPath = buildCommonOpenApiUrlWithPath;
