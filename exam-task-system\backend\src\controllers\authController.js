const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const { User } = require('../models');

// 用户注册
const register = async (req, res, next) => {
  try {
    console.log('注册请求数据:', req.body); // 添加调试日志
    const { username, password, name, email, role } = req.body;

    // 验证必需字段
    if (!username || !password || !email) {
      return res.status(400).json({ message: '用户名、密码和邮箱为必填项' });
    }

    // 创建新用户
    const user = await User.createUser({
      username,
      password,
      name: name || username, // 如果没有name，使用username
      email,
      role: role || 'student' // 默认为学生角色
    });

    console.log('用户创建成功:', user); // 添加调试日志

    // 生成 JWT token
    const token = jwt.sign(
      { id: user.id, username: user.username, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    // 返回用户信息和 token
    res.status(201).json({
      message: '注册成功',
      user,
      token
    });
  } catch (error) {
    console.error('注册失败:', error); // 添加调试日志
    if (error.message === '用户名已存在' || error.message === '邮箱已被使用') {
      return res.status(400).json({ message: error.message });
    }
    next(error);
  }
};

// 用户登录
const login = async (req, res, next) => {
  try {
    console.log('=== 登录请求开始 ===');
    console.log('请求体:', req.body);

    const { email, password } = req.body;

    console.log('开始验证用户:', email);

    // 验证参数
    if (!email || !password) {
      console.log('参数验证失败: 缺少邮箱或密码');
      return res.status(400).json({ message: '请提供邮箱和密码' });
    }

    // 查找用户
    const user = await User.getUserByEmail(email);
    console.log('用户查找结果:', user ? '找到用户' : '用户不存在');

    if (!user) {
      console.log('用户不存在');
      return res.status(401).json({ message: '邮箱或密码错误' });
    }

    console.log('开始验证密码');

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, user.password);
    console.log('密码验证结果:', isValidPassword);

    if (!isValidPassword) {
      console.log('密码验证失败');
      return res.status(401).json({ message: '邮箱或密码错误' });
    }

    console.log('开始生成JWT令牌');

    // 生成 JWT token
    const token = jwt.sign(
      { id: user.id, username: user.username, email: user.email, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    console.log('JWT令牌生成成功');

    // 返回用户信息和 token
    res.json({
      message: '登录成功',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        nickname: user.nickname
      },
      token
    });

    console.log('=== 登录请求完成 ===');
  } catch (error) {
    console.error('=== 登录错误 ===');
    console.error('错误类型:', error.name);
    console.error('错误信息:', error.message);
    console.error('错误堆栈:', error.stack);
    console.error('================');
    next(error);
  }
};

// 获取当前用户信息
const getCurrentUser = async (req, res, next) => {
  try {
    // 用户信息已在认证中间件中添加到请求对象
    const user = req.user;
    
    res.json({ user });
  } catch (error) {
    next(error);
  }
};

// 修改密码
const changePassword = async (req, res, next) => {
  try {
    const { oldPassword, newPassword } = req.body;
    const userId = req.user.id;

    // 验证参数
    if (!oldPassword || !newPassword) {
      return res.status(400).json({ message: '请提供旧密码和新密码' });
    }

    // 获取用户信息
    const user = await User.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 验证旧密码
    const isValidPassword = await bcrypt.compare(oldPassword, user.password);
    if (!isValidPassword) {
      return res.status(400).json({ message: '旧密码错误' });
    }

    // 加密新密码
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

    // 更新密码
    await User.updateUser(userId, { password: hashedNewPassword });

    res.json({ message: '密码修改成功' });
  } catch (error) {
    next(error);
  }
};

// 重置密码（简化版，实际应该发送邮件验证）
const resetPassword = async (req, res, next) => {
  try {
    const { email, newPassword } = req.body;

    // 验证参数
    if (!email || !newPassword) {
      return res.status(400).json({ message: '请提供邮箱和新密码' });
    }

    // 查找用户
    const user = await User.getUserByEmail(email);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 加密新密码
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // 更新密码
    await User.updateUser(user.id, { password: hashedPassword });

    res.json({ message: '密码重置成功' });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  register,
  login,
  getCurrentUser,
  changePassword,
  resetPassword
};