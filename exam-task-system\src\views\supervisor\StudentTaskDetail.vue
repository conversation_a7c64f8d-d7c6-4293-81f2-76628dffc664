<template>
  <div class="student-task-detail">
    <div class="page-header">
      <div class="header-content">
        <div class="back-button">
          <el-button @click="goBack" :icon="ArrowLeft">返回</el-button>
        </div>
        <div class="student-info">
          <el-avatar :size="60">{{ studentName.charAt(0) }}</el-avatar>
          <div class="info">
            <h1>{{ studentName }} 的任务详情</h1>
            <p>{{ studentEmail }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon accepted">
                <el-icon><DocumentAdd /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ taskStats.accepted_tasks || 0 }}</div>
                <div class="stat-label">已领取任务</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon completed">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ taskStats.completed_tasks || 0 }}</div>
                <div class="stat-label">已完成任务</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon points">
                <el-icon><Trophy /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ taskStats.total_points || 0 }}</div>
                <div class="stat-label">总积分</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon time">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ formatStudyTime(taskStats.total_study_time || 0) }}</div>
                <div class="stat-label">总学习时间</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 任务列表 -->
    <div class="tasks-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3>任务列表</h3>
            <div class="filters">
              <el-select v-model="statusFilter" placeholder="任务状态" size="default">
                <el-option label="全部状态" value="" />
                <el-option label="进行中" value="in_progress" />
                <el-option label="已完成" value="completed" />
                <el-option label="已逾期" value="overdue" />
              </el-select>
              <el-button @click="refreshTasks" :icon="RefreshRight">刷新</el-button>
            </div>
          </div>
        </template>

        <el-table :data="filteredTasks" v-loading="loading" style="width: 100%">
          <el-table-column prop="title" label="任务名称" min-width="200">
            <template #default="scope">
              <div class="task-title">
                <el-tag :type="getTaskTypeTag(scope.row.type)" size="small">
                  {{ getTaskTypeLabel(scope.row.type) }}
                </el-tag>
                <span>{{ scope.row.title }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="subject" label="科目" width="100">
            <template #default="scope">
              <el-tag :color="getSubjectColor(scope.row.subject)" effect="light">
                {{ scope.row.subject }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="deadline" label="截止时间" width="150">
            <template #default="scope">
              {{ formatDate(scope.row.deadline) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusLabel(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="points" label="积分" width="80" />
          
          <el-table-column prop="study_time" label="学习时长" width="120">
            <template #default="scope">
              <span>{{ formatStudyTime(scope.row.study_time || 0) }}</span>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button 
                type="primary" 
                size="small" 
                @click="viewTaskDetail(scope.row)"
              >
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div v-if="filteredTasks.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无任务数据" />
        </div>
      </el-card>
    </div>

    <!-- 任务详情对话框 -->
    <el-dialog
      v-model="taskDetailVisible"
      :title="selectedTask?.title"
      width="600px"
    >
      <div v-if="selectedTask" class="task-detail-content">
        <div class="detail-section">
          <h4>任务信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="任务类型">
              <el-tag :type="getTaskTypeTag(selectedTask.type)">
                {{ getTaskTypeLabel(selectedTask.type) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="科目">
              <el-tag :color="getSubjectColor(selectedTask.subject)" effect="light">
                {{ selectedTask.subject }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="难度">
              <el-rate 
                v-model="selectedTask.difficulty" 
                disabled 
                :max="3"
                text-color="#ff9900"
              />
            </el-descriptions-item>
            <el-descriptions-item label="积分">{{ selectedTask.points }}</el-descriptions-item>
            <el-descriptions-item label="截止时间">{{ formatDate(selectedTask.deadline) }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusType(selectedTask.status)">
                {{ getStatusLabel(selectedTask.status) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section">
          <h4>任务描述</h4>
          <p>{{ selectedTask.description }}</p>
        </div>

        <div v-if="selectedTask.completionDescription" class="detail-section">
          <h4>完成情况</h4>
          <p>{{ selectedTask.completionDescription }}</p>
          <div v-if="selectedTask.completionDate" class="completion-info">
            <p><strong>完成时间：</strong>{{ formatDate(selectedTask.completionDate) }}</p>
            <p v-if="selectedTask.selfRating"><strong>自评分数：</strong>{{ selectedTask.selfRating }}/5</p>
            <p v-if="selectedTask.reflection"><strong>学习反思：</strong>{{ selectedTask.reflection }}</p>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  ArrowLeft, DocumentAdd, CircleCheck, Trophy, Timer, RefreshRight
} from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { bindingAPI } from '@/api/binding';
import { taskAPI } from '@/api/task';
import dayjs from 'dayjs';

const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(false);
const taskStats = ref({});
const tasks = ref([]);
const statusFilter = ref('');
const taskDetailVisible = ref(false);
const selectedTask = ref(null);

// 从路由参数获取学生信息
const studentId = route.params.studentId;
const studentName = route.query.studentName || '未知学生';
const studentEmail = route.query.studentEmail || '';

// 计算属性
const filteredTasks = computed(() => {
  if (!statusFilter.value) return tasks.value;
  return tasks.value.filter(task => task.status === statusFilter.value);
});

// 方法
const goBack = () => {
  router.go(-1);
};

const loadStudentTaskStats = async () => {
  try {
    loading.value = true;
    const response = await bindingAPI.getBindingInfo({
      student_id: studentId
    });
    
    if (response && response.data) {
      taskStats.value = response.data.task_stats || {};
    }
  } catch (error) {
    console.error('加载学生任务统计失败:', error);
    ElMessage.error('加载任务统计失败');
  } finally {
    loading.value = false;
  }
};

const loadStudentTasks = async () => {
  try {
    loading.value = true;

    // 调用API获取学生任务列表
    const response = await taskAPI.getStudentTasks(studentId);
    if (response && response.data) {
      tasks.value = response.data;
      console.log('学生任务加载成功，任务数量:', tasks.value.length);
    } else {
      tasks.value = [];
      console.log('未获取到学生任务数据');
    }
  } catch (error) {
    console.error('加载学生任务失败:', error);
    tasks.value = [];
    ElMessage.error('加载任务列表失败');
  } finally {
    loading.value = false;
  }
};

const refreshTasks = async () => {
  await Promise.all([
    loadStudentTaskStats(),
    loadStudentTasks()
  ]);
  ElMessage.success('刷新成功');
};

const viewTaskDetail = (task) => {
  selectedTask.value = task;
  taskDetailVisible.value = true;
};

// 工具方法
const getTaskTypeLabel = (type) => {
  const labels = {
    daily: '每日',
    weekly: '每周',
    special: '专项'
  };
  return labels[type] || type;
};

const getTaskTypeTag = (type) => {
  const tags = {
    daily: 'primary',
    weekly: 'success',
    special: 'warning'
  };
  return tags[type] || '';
};

const getSubjectColor = (subject) => {
  const colors = {
    '数学': '#409eff',
    '英语': '#f56c6c',
    '政治': '#e6a23c',
    '专业课': '#67c23a'
  };
  return colors[subject] || '#909399';
};

const getStatusLabel = (status) => {
  const labels = {
    in_progress: '进行中',
    completed: '已完成',
    overdue: '已逾期',
    submitted: '已提交'
  };
  return labels[status] || status;
};

const getStatusType = (status) => {
  const types = {
    in_progress: 'primary',
    completed: 'success',
    overdue: 'danger',
    submitted: 'warning'
  };
  return types[status] || '';
};

const formatDate = (dateString) => {
  return dayjs(dateString).format('MM-DD HH:mm');
};

const formatStudyTime = (minutes) => {
  if (!minutes || minutes === 0) return '0分钟';

  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;

  if (hours > 0) {
    return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`;
  } else {
    return `${mins}分钟`;
  }
};

// 页面初始化
onMounted(() => {
  loadStudentTaskStats();
  loadStudentTasks();
});
</script>

<style scoped>
.student-task-detail {
  padding: 20px;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  color: white;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-button .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.student-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.student-info h1 {
  margin: 0;
  font-size: 24px;
}

.student-info p {
  margin: 4px 0 0 0;
  opacity: 0.9;
}

.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.accepted {
  background: linear-gradient(135deg, #409eff, #2563eb);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #67c23a, #16a34a);
}

.stat-icon.points {
  background: linear-gradient(135deg, #e6a23c, #ea580c);
}

.stat-icon.rate {
  background: linear-gradient(135deg, #909399, #6b7280);
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.tasks-section .el-card {
  border-radius: 12px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h3 {
  margin: 0;
}

.filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

.task-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  margin-left: 8px;
}

.empty-state {
  padding: 40px;
  text-align: center;
}

.task-detail-content {
  max-height: 500px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.completion-info {
  margin-top: 12px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 8px;
}

.completion-info p {
  margin: 4px 0;
  font-size: 14px;
}
</style>
