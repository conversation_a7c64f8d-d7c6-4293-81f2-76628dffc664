<template>
  <div class="university-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-text">
          <h1 class="page-title">我的考研之路</h1>
          <p class="page-subtitle">追求梦想，永不止步</p>
        </div>
        <el-button
          type="primary"
          size="large"
          class="header-button"
          @click="showUniversityDialog"
        >
          <el-icon><School /></el-icon>
          {{ university ? '修改目标' : '设置目标' }}
        </el-button>
      </div>
    </div>

    <!-- 目标院校卡片 -->
    <div v-if="university" class="university-card">
      <div class="university-header">
        <div class="university-badge">
          <el-icon size="24"><Trophy /></el-icon>
        </div>
        <div class="university-info">
          <h2 class="university-name">{{ university.name }}</h2>
          <div class="university-meta">
            <span class="meta-item">
              <el-icon><Location /></el-icon>
              {{ university.location }}
            </span>
            <span class="meta-item">
              <el-icon><School /></el-icon>
              {{ university.department }}
            </span>
            <span class="meta-item">
              <el-icon><Calendar /></el-icon>
              {{ university.examDate }}
            </span>
          </div>
        </div>
      </div>

      <!-- 倒计时和进度 -->
      <div class="countdown-section">
        <div class="countdown-card">
          <div class="countdown-number">{{ daysRemaining }}</div>
          <div class="countdown-label">天后考试</div>
        </div>
        <div class="progress-card">
          <div class="progress-header">
            <span>备考进度</span>
            <span class="progress-percentage">{{ progressPercentage }}%</span>
          </div>
          <el-progress
            :percentage="progressPercentage"
            :color="progressColor"
            :stroke-width="8"
            :show-text="false"
          />
          <div class="progress-status">{{ getProgressStatus() }}</div>
        </div>
      </div>
      
      <!-- 学习成就统计 -->
      <div class="achievement-stats-section">
        <div class="section-header">
          <h3>学习成就</h3>
          <el-button size="small" @click="refreshTaskStats" :icon="RefreshRight">刷新</el-button>
        </div>
        <div class="achievement-container">
          <div class="achievement-overview">
            <div class="achievement-card primary">
              <div class="achievement-icon">
                <el-icon size="32"><Trophy /></el-icon>
              </div>
              <div class="achievement-content">
                <div class="achievement-number">{{ taskStats.accepted_tasks || 0 }}</div>
                <div class="achievement-label">领取任务</div>
              </div>
            </div>
            <div class="achievement-card success">
              <div class="achievement-icon">
                <el-icon size="32"><CircleCheck /></el-icon>
              </div>
              <div class="achievement-content">
                <div class="achievement-number">{{ taskStats.completed_tasks || 0 }}</div>
                <div class="achievement-label">完成任务</div>
              </div>
            </div>
            <div class="achievement-card warning">
              <div class="achievement-icon">
                <el-icon size="32"><Star /></el-icon>
              </div>
              <div class="achievement-content">
                <div class="achievement-number">{{ taskStats.total_points || 0 }}</div>
                <div class="achievement-label">总积分</div>
              </div>
            </div>
            <div class="achievement-card info">
              <div class="achievement-icon">
                <el-icon size="32"><DataAnalysis /></el-icon>
              </div>
              <div class="achievement-content">
                <div class="achievement-number">{{ taskStats.completion_rate || 0 }}%</div>
                <div class="achievement-label">完成率</div>
              </div>
            </div>
          </div>

          <!-- 完成率进度条 -->
          <div class="completion-progress">
            <div class="progress-header">
              <span>任务完成进度</span>
              <span class="progress-text">{{ taskStats.completed_tasks || 0 }}/{{ taskStats.accepted_tasks || 0 }}</span>
            </div>
            <el-progress
              :percentage="taskStats.completion_rate || 0"
              :color="getCompletionColor(taskStats.completion_rate || 0)"
              :stroke-width="12"
              :show-text="false"
            />
          </div>
        </div>
      </div>

      <!-- 本周学习统计 -->
      <div class="weekly-stats-section">
        <div class="section-header">
          <h3>本周学习统计</h3>
          <el-button size="small" @click="calculateWeeklyStats" :icon="RefreshRight">刷新</el-button>
        </div>
        <div class="weekly-stats-container">
          <div class="stats-overview">
            <div class="stats-card">
              <div class="stats-number">{{ weeklyStats.totalCompleted }}</div>
              <div class="stats-label">本周完成任务</div>
            </div>
            <div class="stats-card">
              <div class="stats-number">{{ weeklyStats.totalHours }}h</div>
              <div class="stats-label">学习时长</div>
            </div>
            <div class="stats-card">
              <div class="stats-number">{{ weeklyStats.totalPoints }}</div>
              <div class="stats-label">获得积分</div>
            </div>
          </div>
          
          <div class="stats-details">
            <div class="stats-column">
              <h4>科目分布</h4>
              <div v-if="weeklyStats.subjectDistribution.length > 0" class="subject-tags">
                <el-tag 
                  v-for="(subject, index) in weeklyStats.subjectDistribution" 
                  :key="index"
                  :color="getSubjectColor(subject.name)"
                  effect="light"
                  class="subject-tag"
                >
                  {{ subject.name }}: {{ subject.count }}任务
                </el-tag>
              </div>
              <div v-else class="empty-distribution">
                暂无科目分布数据
              </div>
            </div>
            
            <div class="stats-column">
              <h4>学习建议</h4>
              <div v-if="weeklyStats.needsAttention.length > 0" class="attention-subjects">
                <p>需要加强:</p>
                <div class="subject-tags">
                  <el-tag 
                    v-for="(subject, index) in weeklyStats.needsAttention" 
                    :key="index"
                    :color="getSubjectColor(subject)"
                    effect="dark"
                    class="attention-tag"
                  >
                    {{ subject }}
                  </el-tag>
                </div>
              </div>
              <div v-else class="balanced-message">
                <el-icon><Check /></el-icon>
                学习均衡，继续保持!
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 各科目进度 -->
      <div class="subjects-progress-section">
        <div class="section-header">
          <h3>各科目学习进度</h3>
          <el-button size="small" @click="calculateSubjectsProgress" :icon="RefreshRight">刷新</el-button>
        </div>
        <div class="subjects-grid">
          <div v-for="(subject, index) in subjectsProgress" :key="index" class="subject-card">
            <div class="subject-card-header">
              <div class="subject-icon" :style="getSubjectIconStyle(subject.name)">
                <el-icon size="20"><DataAnalysis /></el-icon>
              </div>
              <div class="subject-info">
                <h4 class="subject-name">{{ subject.name }}</h4>
                <p class="subject-stats">{{ subject.completedTasks }}/{{ subject.totalTasks }} 已完成</p>
              </div>
              <div class="subject-percentage">{{ subject.percentage }}%</div>
            </div>
            <div class="subject-progress">
              <el-progress
                :percentage="subject.percentage"
                :color="getSubjectColor(subject.name)"
                :stroke-width="6"
                :show-text="false"
              />
            </div>
            <div class="subject-status">
              <span :class="getStatusClass(subject.percentage)">
                {{ getStatusText(subject.percentage) }}
              </span>
            </div>
          </div>

          <div v-if="subjectsProgress.length === 0" class="no-subjects-card">
            <div class="empty-icon">
              <el-icon size="48"><School /></el-icon>
            </div>
            <h4>暂无科目数据</h4>
            <p>请先领取并完成相关任务来查看科目进度</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-content">
        <div class="empty-icon">
          <el-icon size="80"><School /></el-icon>
        </div>
        <h2 class="empty-title">开启你的考研之路</h2>
        <p class="empty-description">设置目标院校，制定学习计划，追求梦想从这里开始</p>
        <el-button
          type="primary"
          size="large"
          class="empty-button"
          @click="showUniversityDialog"
        >
          <el-icon><Trophy /></el-icon>
          设置目标院校
        </el-button>
      </div>
    </div>

    <!-- 目标院校设置对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="设置目标院校"
      width="80%"
    >
      <el-form :model="universityForm" label-position="top">
        <el-form-item label="院校名称">
          <el-input v-model="universityForm.name" placeholder="请输入目标院校名称" />
        </el-form-item>
        <el-form-item label="所在地区">
          <el-input v-model="universityForm.location" placeholder="请输入院校所在地区" />
        </el-form-item>
        <el-form-item label="目标专业">
          <el-input v-model="universityForm.department" placeholder="请输入目标专业" />
        </el-form-item>
        <el-form-item label="考试日期">
          <el-date-picker
            v-model="universityForm.examDate"
            type="date"
            placeholder="选择考试日期"
            format="YYYY/MM/DD"
            value-format="YYYY/MM/DD"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveUniversity">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { Location, School, Calendar, RefreshRight, Check, Trophy, CircleCheck, Star, DataAnalysis } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { bindingAPI } from '../../api/binding';

// 常量定义
const STORAGE_KEY = 'targetUniversity';
const DEFAULT_STUDY_PERIOD = 365; // 默认备考周期为一年

// 状态管理
const university = ref(null);
const dialogVisible = ref(false);
const universityForm = ref({
  name: '',
  location: '',
  department: '',
  examDate: ''
});

// 任务统计数据
const taskStats = ref({
  accepted_tasks: 0,
  completed_tasks: 0,
  total_points: 0,
  completion_rate: 0
});

// 科目进度数据
const subjectsProgress = ref([]);

// 周学习统计数据
const weeklyStats = ref({
  totalCompleted: 0,
  totalHours: 0,
  totalPoints: 0,
  subjectDistribution: [],
  needsAttention: []
});

// 计算属性
// 计算距离考试的天数
const daysRemaining = computed(() => {
  if (!university.value?.examDate) return 0;
  
  const examDate = new Date(university.value.examDate);
  const today = new Date();
  const diffTime = examDate - today;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays > 0 ? diffDays : 0;
});

// 计算进度百分比（动态调整算法）
const progressPercentage = computed(() => {
  if (!university.value?.examDate) return 0;
  
  const examDate = new Date(university.value.examDate);
  const today = new Date();
  
  const diffTime = examDate - today;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays <= 0) return 100;
  
  // 动态调整算法：确保考试当天进度为100%
  const remainingPercentage = 100 - Math.min(100, Math.max(0, (diffDays / DEFAULT_STUDY_PERIOD) * 100));
  
  // 应用非线性增长：开始慢，接近考试日期时快
  return Math.min(100, Math.pow(remainingPercentage, 0.85) * 1.2);
});

// 进度条颜色 - 优化色彩搭配
const progressColor = computed(() => {
  const percentage = progressPercentage.value;
  if (percentage < 25) return ['#ef4444', '#dc2626']; // 红色渐变
  if (percentage < 50) return ['#f97316', '#ea580c']; // 橙色渐变
  if (percentage < 75) return ['#eab308', '#ca8a04']; // 黄色渐变
  return ['#22c55e', '#16a34a']; // 绿色渐变
});

// 方法
// 显示设置对话框
const showUniversityDialog = () => {
  if (university.value) {
    universityForm.value = { ...university.value };
  } else {
    // 重置表单
    universityForm.value = {
      name: '',
      location: '',
      department: '',
      examDate: ''
    };
  }
  dialogVisible.value = true;
};

// 保存目标院校
const saveUniversity = () => {
  // 表单验证
  if (!universityForm.value.name || !universityForm.value.examDate) {
    ElMessage.warning('请至少填写院校名称和考试日期');
    return;
  }
  
  try {
    // 更新数据
    university.value = { ...universityForm.value };
    
    // 保存到本地存储
    localStorage.setItem(STORAGE_KEY, JSON.stringify(university.value));
    
    // 关闭对话框并提示
    dialogVisible.value = false;
    ElMessage.success('目标院校设置成功');
  } catch (error) {
    console.error('保存目标院校失败:', error);
    ElMessage.error('保存失败，请重试');
  }
};

// 进度条文字格式化
const progressFormat = (percentage) => {
  return `${Math.round(percentage)}%`;
};

// 获取科目颜色 - 柔和浅色系
const getSubjectColor = (subject) => {
  const colorMap = {
    '数学': ['#93c5fd', '#60a5fa'], // 浅蓝色渐变
    '英语': ['#fca5a5', '#f87171'], // 浅红色渐变
    '政治': ['#fdba74', '#fb923c'], // 浅橙色渐变
    '专业课': ['#86efac', '#4ade80'], // 浅绿色渐变
    '计算机': ['#c4b5fd', '#a78bfa'], // 浅紫色渐变
    '管理学': ['#67e8f9', '#22d3ee'], // 浅青色渐变
    '经济学': ['#fde047', '#facc15'], // 浅黄色渐变
    '法学': ['#f9a8d4', '#f472b6']  // 浅粉色渐变
  };
  return colorMap[subject] || ['#a5b4fc', '#818cf8']; // 默认浅靛蓝渐变
};

// 获取科目图标样式
const getSubjectIconStyle = (subject) => {
  const colors = getSubjectColor(subject);
  return {
    background: `linear-gradient(135deg, ${colors[0]}, ${colors[1]})`
  };
};

// 获取状态样式类
const getStatusClass = (percentage) => {
  if (percentage < 30) return 'status-danger';
  if (percentage < 60) return 'status-warning';
  if (percentage < 80) return 'status-normal';
  return 'status-success';
};

// 获取状态文本
const getStatusText = (percentage) => {
  if (percentage < 30) return '需加强';
  if (percentage < 60) return '继续努力';
  if (percentage < 80) return '进展良好';
  return '优秀';
};

// 计算科目进度
const calculateSubjectsProgress = () => {
  try {
    // 获取我的任务列表
    const myTasks = JSON.parse(localStorage.getItem('myTasks') || '[]');
    
    // 按科目分组统计
    const subjectStats = {};
    
    // 初始化四个主要科目
    ['数学', '英语', '政治', '专业课'].forEach(subject => {
      subjectStats[subject] = {
        name: subject,
        totalTasks: 0,
        completedTasks: 0,
        percentage: 0
      };
    });
    
    // 如果没有任何任务数据，添加默认科目
    if (myTasks.length === 0) {
      subjectsProgress.value = [
        { name: '数学', totalTasks: 0, completedTasks: 0, percentage: 0 },
        { name: '英语', totalTasks: 0, completedTasks: 0, percentage: 0 },
        { name: '政治', totalTasks: 0, completedTasks: 0, percentage: 0 },
        { name: '专业课', totalTasks: 0, completedTasks: 0, percentage: 0 }
      ];
      return;
    }
    
    // 统计任务数据
    myTasks.forEach(task => {
      const subject = task.subject;
      if (subject && subjectStats[subject]) {
        subjectStats[subject].totalTasks++;
        
        if (task.status === 'completed') {
          subjectStats[subject].completedTasks++;
        }
      }
    });
    
    // 计算百分比
    Object.keys(subjectStats).forEach(subject => {
      const stats = subjectStats[subject];
      stats.percentage = stats.totalTasks > 0 
        ? Math.round((stats.completedTasks / stats.totalTasks) * 100) 
        : 0;
    });
    
    // 转换为数组并排序 - 不再过滤掉没有任务的科目
    subjectsProgress.value = Object.values(subjectStats)
      .sort((a, b) => b.percentage - a.percentage);
    
    // 确保四个主要科目都显示出来
    const mainSubjects = ['数学', '英语', '政治', '专业课'];
    const displayedSubjects = subjectsProgress.value.map(s => s.name);
    
    // 检查是否有缺失的科目
    mainSubjects.forEach(subject => {
      if (!displayedSubjects.includes(subject)) {
        // 添加缺失的科目
        subjectsProgress.value.push({
          name: subject,
          totalTasks: 0,
          completedTasks: 0,
          percentage: 0
        });
      }
    });
    
    // 重新排序
    subjectsProgress.value.sort((a, b) => {
      // 首先按照百分比排序
      if (b.percentage !== a.percentage) {
        return b.percentage - a.percentage;
      }
      // 如果百分比相同，按照科目顺序排序
      const order = { '数学': 0, '英语': 1, '政治': 2, '专业课': 3 };
      return order[a.name] - order[b.name];
    });
    
  } catch (error) {
    console.error('计算科目进度失败:', error);
    subjectsProgress.value = [];
  }
};

// 计算周学习统计
const calculateWeeklyStats = () => {
  try {
    // 获取我的任务列表
    const myTasks = JSON.parse(localStorage.getItem('myTasks') || '[]');
    
    // 获取当前日期和一周前的日期
    const now = new Date();
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    
    // 过滤出本周完成的任务
    const thisWeekTasks = myTasks.filter(task => {
      if (task.status !== 'completed') return false;
      
      const completedDate = task.endTime ? new Date(task.endTime) : 
                           (task.submitTime ? new Date(task.submitTime) : null);
      
      if (!completedDate) return false;
      
      return completedDate >= oneWeekAgo && completedDate <= now;
    });
    
    // 计算总完成任务数
    weeklyStats.value.totalCompleted = thisWeekTasks.length;
    
    // 计算总学习时长（小时）
    weeklyStats.value.totalHours = thisWeekTasks.reduce((total, task) => {
      return total + (task.totalTimeSpent || 0) / 60; // 转换为小时
    }, 0).toFixed(1);
    
    // 计算总获得积分
    weeklyStats.value.totalPoints = thisWeekTasks.reduce((total, task) => {
      return total + (task.points || 0);
    }, 0);
    
    // 计算科目分布
    const subjectCounts = {};
    thisWeekTasks.forEach(task => {
      const subject = task.subject;
      if (subject) {
        subjectCounts[subject] = (subjectCounts[subject] || 0) + 1;
      }
    });
    
    // 转换为数组并计算百分比
    const totalCount = thisWeekTasks.length;
    weeklyStats.value.subjectDistribution = Object.entries(subjectCounts).map(([name, count]) => {
      return {
        name,
        count,
        percentage: totalCount > 0 ? Math.round((count / totalCount) * 100) : 0
      };
    }).sort((a, b) => b.count - a.count);
    
    // 确定需要加强的科目
    const allSubjects = ['数学', '英语', '政治', '专业课'];
    const coveredSubjects = new Set(weeklyStats.value.subjectDistribution.map(s => s.name));
    
    // 找出本周没有完成任务的科目
    const missingSubjects = allSubjects.filter(subject => !coveredSubjects.has(subject));
    
    // 找出完成任务较少的科目（低于平均值的20%）
    const averageCount = totalCount / coveredSubjects.size;
    const lowPerformingSubjects = weeklyStats.value.subjectDistribution
      .filter(s => s.count < averageCount * 0.8)
      .map(s => s.name);
    
    // 合并需要关注的科目
    weeklyStats.value.needsAttention = [...missingSubjects, ...lowPerformingSubjects];
    
    // 如果所有科目都没有任务，建议关注所有科目
    if (thisWeekTasks.length === 0) {
      weeklyStats.value.needsAttention = allSubjects;
    }
    
  } catch (error) {
    console.error('计算周学习统计失败:', error);
    // 设置默认值
    weeklyStats.value = {
      totalCompleted: 0,
      totalHours: 0,
      totalPoints: 0,
      subjectDistribution: [],
      needsAttention: ['数学', '英语', '政治', '专业课']
    };
  }
};

// 获取任务统计数据
const refreshTaskStats = async () => {
  try {
    // 这里应该调用绑定信息API获取任务统计
    // 暂时使用模拟数据
    const response = await bindingAPI.getMyBinding();

    if (response && response.data && response.data.task_stats) {
      taskStats.value = response.data.task_stats;
      console.log('获取到真实任务统计:', taskStats.value);
    } else {
      // 如果没有绑定信息或任务统计，使用默认值
      taskStats.value = {
        accepted_tasks: 0,
        completed_tasks: 0,
        total_points: 0,
        completion_rate: 0
      };
      console.log('未获取到任务统计，使用默认值');
    }
  } catch (error) {
    console.error('获取任务统计失败:', error);
    // 使用默认值
    taskStats.value = {
      accepted_tasks: 0,
      completed_tasks: 0,
      total_points: 0,
      completion_rate: 0
    };
  }
};

// 获取完成率颜色 - 优化色彩搭配
const getCompletionColor = (percentage) => {
  if (percentage >= 80) return ['#22c55e', '#16a34a']; // 绿色渐变
  if (percentage >= 60) return ['#eab308', '#ca8a04']; // 黄色渐变
  if (percentage >= 40) return ['#f97316', '#ea580c']; // 橙色渐变
  return ['#ef4444', '#dc2626']; // 红色渐变
};

// 获取进度状态文本
const getProgressStatus = () => {
  const percentage = progressPercentage.value;
  if (percentage < 20) return '刚刚开始，加油！';
  if (percentage < 40) return '稳步前进中...';
  if (percentage < 60) return '进展良好！';
  if (percentage < 80) return '即将冲刺！';
  return '准备充分，信心满满！';
};

// 生命周期钩子
onMounted(() => {
  // 从本地存储加载数据
  loadUniversityData();

  // 计算科目进度
  calculateSubjectsProgress();

  // 计算周学习统计
  calculateWeeklyStats();

  // 获取任务统计
  refreshTaskStats();
});

// 监听我的任务变化，更新数据
watch(() => localStorage.getItem('myTasks'), () => {
  calculateSubjectsProgress();
  calculateWeeklyStats();
});

// 辅助函数
// 从本地存储加载目标院校数据
const loadUniversityData = () => {
  try {
    const savedUniversity = localStorage.getItem(STORAGE_KEY);
    if (savedUniversity) {
      university.value = JSON.parse(savedUniversity);
    }
  } catch (error) {
    console.error('加载目标院校数据失败:', error);
    ElMessage.error('加载数据失败，请刷新页面重试');
  }
};
</script>

<style scoped>
.university-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
  padding: 0;
}

/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 50%, #a5b4fc 100%);
  padding: 40px 24px;
  color: #1e293b;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
  background: linear-gradient(45deg, #1e293b, #475569);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.7;
  font-weight: 300;
  color: #64748b;
}

.header-button {
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.5);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  color: #475569;
}

.header-button:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 目标院校卡片样式 */
.university-card {
  max-width: 1200px;
  margin: -20px auto 32px;
  background: white;
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.university-header {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 32px;
}

.university-badge {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #fde047, #facc15);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #92400e;
  box-shadow: 0 8px 24px rgba(253, 224, 71, 0.3);
}

.university-info {
  flex: 1;
}

.university-name {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 12px 0;
  color: #2c3e50;
}

.university-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
}

/* 倒计时和进度样式 */
.countdown-section {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 32px;
  align-items: center;
  margin-bottom: 32px;
}

.countdown-card {
  text-align: center;
  background: linear-gradient(135deg, #fecaca, #fca5a5);
  color: #991b1b;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(254, 202, 202, 0.4);
}

.countdown-number {
  font-size: 48px;
  font-weight: 900;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(153, 27, 27, 0.1);
}

.countdown-label {
  font-size: 14px;
  font-weight: 500;
  opacity: 0.9;
}

.progress-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 24px;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(226, 232, 240, 0.5);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 600;
  color: #2c3e50;
}

.progress-percentage {
  font-size: 18px;
  color: #6366f1;
  font-weight: 700;
}

.progress-status {
  margin-top: 8px;
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.countdown {
  margin-top: 16px;
  text-align: center;
  color: #606266;
}

.countdown .days {
  font-size: 20px;
  font-weight: bold;
  color: #4263eb;
}

.empty-state {
  margin-top: 40px;
  text-align: center;
}

/* 学习成就统计样式 */
.achievement-stats-section {
  margin-top: 32px;
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 50%, #a5b4fc 100%);
  border-radius: 20px;
  padding: 24px;
  color: #1e293b;
  box-shadow: 0 8px 32px rgba(160, 180, 252, 0.3);
}

.achievement-stats-section .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.achievement-stats-section .section-header h3 {
  color: #1e293b;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.achievement-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.achievement-card {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  border: 1px solid rgba(226, 232, 240, 0.5);
  transition: all 0.3s ease;
}

.achievement-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.achievement-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
}

.achievement-card.primary .achievement-icon {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  color: #1d4ed8;
}

.achievement-card.success .achievement-icon {
  background: linear-gradient(135deg, #d1fae5, #a7f3d0);
  color: #047857;
}

.achievement-card.warning .achievement-icon {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  color: #92400e;
}

.achievement-card.info .achievement-icon {
  background: linear-gradient(135deg, #ede9fe, #ddd6fe);
  color: #6b21a8;
}

.achievement-content {
  flex: 1;
}

.achievement-number {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
  color: #1e293b;
}

.achievement-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.completion-progress {
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  color: #1e293b;
  font-weight: 500;
}

.progress-text {
  font-size: 14px;
  color: #64748b;
}

/* 周学习统计样式 */
.weekly-stats-section {
  margin-top: 30px;
}

.weekly-stats-container {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.stats-overview {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16px;
}

.stats-card {
  text-align: center;
  padding: 10px;
  border-radius: 8px;
  background-color: #f8f9fa;
  flex: 1;
  margin: 0 4px;
}

.stats-number {
  font-size: 24px;
  font-weight: 700;
  color: #4263eb;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #606266;
}

.stats-details {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.stats-column {
  flex: 1;
  min-width: 140px;
}

.stats-column h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #303133;
}

.subject-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.subject-tag, .attention-tag {
  margin-bottom: 6px;
  font-size: 12px;
}

.empty-distribution {
  color: #909399;
  font-size: 12px;
}

.attention-subjects p {
  margin: 0 0 6px 0;
  font-size: 12px;
  color: #606266;
}

.balanced-message {
  display: flex;
  align-items: center;
  color: #67c23a;
  font-size: 12px;
  font-weight: 500;
}

.balanced-message .el-icon {
  margin-right: 4px;
}

/* 科目进度样式 */
.subjects-progress-section {
  margin-top: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
}

/* 科目进度网格样式 */
.subjects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.subject-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.subject-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.subject-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.subject-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  /* 背景色通过内联样式动态设置 */
}

.subject-info {
  flex: 1;
}

.subject-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.subject-stats {
  font-size: 14px;
  color: #64748b;
  margin: 0;
}

.subject-percentage {
  font-size: 18px;
  font-weight: 700;
  color: #6366f1;
}

.subject-progress {
  margin: 16px 0;
}

.subject-status {
  text-align: center;
}

.subject-status span {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid currentColor;
}

.no-subjects-card {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.no-subjects-card .empty-icon {
  margin-bottom: 16px;
  color: #94a3b8;
}

.no-subjects-card h4 {
  font-size: 18px;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.no-subjects-card p {
  color: #64748b;
  margin: 0;
}

/* 空状态样式 */
.empty-state {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 24px;
}

.empty-content {
  text-align: center;
  max-width: 400px;
}

.empty-icon {
  margin-bottom: 24px;
  color: #94a3b8;
}

.empty-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 12px 0;
}

.empty-description {
  font-size: 16px;
  color: #64748b;
  margin: 0 0 32px 0;
  line-height: 1.6;
}

.empty-button {
  padding: 12px 32px;
  font-size: 16px;
  border-radius: 12px;
}

.subject-details {
  display: flex;
  justify-content: space-between;
  margin-top: 6px;
  font-size: 12px;
}

.completed-tasks {
  color: #909399;
}

.subject-status {
  font-weight: 500;
}

.status-danger {
  color: #dc2626;
  background: rgba(254, 202, 202, 0.6);
}

.status-warning {
  color: #ea580c;
  background: rgba(253, 186, 116, 0.6);
}

.status-normal {
  color: #ca8a04;
  background: rgba(254, 240, 138, 0.6);
}

.status-success {
  color: #16a34a;
  background: rgba(187, 247, 208, 0.6);
}

.no-subjects {
  text-align: center;
  color: #909399;
  padding: 20px 0;
}
</style>