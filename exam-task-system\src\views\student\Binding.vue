<template>
  <div class="card-container">
    <h2 class="text-xl font-medium mb-6">绑定管理</h2>
    
    <div v-if="loading" class="py-10">
      <el-skeleton :rows="5" animated />
    </div>
    
    <template v-else>
      <!-- 已绑定状态 -->
      <div v-if="binding" class="mb-6">
        <el-alert type="success" :closable="false" show-icon>
          <div class="flex items-center justify-between">
            <span>您已成功绑定监督者</span>
            <el-tag type="success">已绑定</el-tag>
          </div>
        </el-alert>
        
        <div class="supervisor-card">
          <div class="supervisor-header">
            <el-avatar :size="72" class="supervisor-avatar">
              <el-icon size="32"><UserFilled /></el-icon>
            </el-avatar>

            <div class="supervisor-info">
              <h3 class="supervisor-name">
                {{ binding.supervisor?.nickname || binding.supervisor?.username || '监督者' }}
              </h3>
              <div class="supervisor-role">
                <el-tag type="success" size="small">监督者</el-tag>
              </div>
              <div class="supervisor-email" v-if="binding.supervisor?.email">
                <el-icon size="14"><Key /></el-icon>
                {{ binding.supervisor.email }}
              </div>
              <div class="binding-time">
                <el-icon size="14"><Calendar /></el-icon>
                绑定时间：{{ formatDate(binding.created_at) }}
              </div>
            </div>
          </div>

          <div class="supervisor-stats">
            <div class="stat-item">
              <div class="stat-value">{{ binding.task_count || 0 }}</div>
              <div class="stat-label">发布任务</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ binding.days_count || calculateBindingDays() }}</div>
              <div class="stat-label">绑定天数</div>
            </div>
          </div>

          <div class="supervisor-actions">
            <el-button type="danger" plain @click="confirmUnbind">
              <el-icon><Delete /></el-icon>
              解除绑定
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 未绑定状态 -->
      <div v-else>
        <el-alert type="warning" :closable="false" show-icon>
          <div class="flex items-center justify-between">
            <span>您尚未绑定监督者，请输入绑定码完成绑定</span>
            <el-tag type="warning">未绑定</el-tag>
          </div>
        </el-alert>
        
        <div class="form-container mt-8">
          <!-- 成功动画 -->
          <div v-if="showSuccessAnimation" class="success-animation mb-6">
            <div class="success-icon">
              <el-icon size="48" color="#67C23A"><SuccessFilled /></el-icon>
            </div>
            <div class="success-text">绑定成功！</div>
          </div>

          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-width="0"
            @submit.prevent="confirmBinding"
          >
            <el-form-item prop="bindCode">
              <el-input
                v-model="form.bindCode"
                placeholder="请输入绑定码"
                size="large"
                maxlength="6"
                class="text-center"
              >
                <template #prefix>
                  <el-icon><Key /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            
            <el-form-item>
              <el-button
                type="primary"
                size="large"
                class="w-full"
                :loading="isSubmitting"
                @click="confirmBinding"
              >
                <span v-if="!isSubmitting">确认绑定</span>
                <span v-else>{{ loadingText }}</span>
              </el-button>
            </el-form-item>
          </el-form>
          
          <div class="text-center text-gray-500 text-sm mt-4">
            请向监督者获取绑定码，绑定码由监督者生成
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { toast } from 'vue3-toastify'
import { UserFilled, Key, Delete, SuccessFilled, Calendar } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { bindingAPI } from '@/api/binding'
import { useAuthStore } from '@/stores/auth'
import { useStudentStore } from '@/stores/student'

const authStore = useAuthStore()
const studentStore = useStudentStore()

const loading = ref(true)
const isSubmitting = ref(false)
const formRef = ref(null)
const loadingText = ref('确认绑定')
const showSuccessAnimation = ref(false)

// 绑定信息
const binding = ref(null)

// 表单数据
const form = reactive({
  bindCode: ''
})

// 表单验证规则
const rules = {
  bindCode: [
    { required: true, message: '请输入绑定码', trigger: 'blur' },
    { pattern: /^[A-Z0-9]{6}$/, message: '绑定码为6位字母或数字', trigger: 'blur' }
  ]
}

// {{ AURA-X: Modify - 使用统一的学生端数据源. Approval: 寸止(ID:1734684000). }}
// 获取绑定信息
const getBindingInfo = async () => {
  loading.value = true

  try {
    // 使用统一的学生端数据源
    await studentStore.loadBindingInfo()
    binding.value = studentStore.bindingInfo

    console.log('绑定信息数据:', binding.value) // 调试日志
  } catch (error) {
    console.error('获取绑定信息错误:', error)
    // 404错误表示没有绑定关系，这是正常的
    if (error.response?.status !== 404) {
      toast.error('获取绑定信息失败，请重试')
    }
    binding.value = null
  } finally {
    loading.value = false
  }
}

// 确认绑定
const confirmBinding = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    isSubmitting.value = true

    // 优化加载状态提示
    loadingText.value = '正在验证绑定码...'
    await new Promise(resolve => setTimeout(resolve, 500)) // 短暂延迟让用户看到状态

    loadingText.value = '正在建立绑定关系...'

    const response = await bindingAPI.confirmBinding({
      student_id: authStore.user.id, // 从认证状态获取用户ID
      bind_code: form.bindCode
    })

    console.log('绑定API响应:', response) // 调试日志

    if (response && (response.message || response.data)) {
      loadingText.value = '绑定成功！'

      // 显示成功动画
      showSuccessAnimation.value = true

      // 成功提示
      ElMessage({
        message: '🎉 绑定成功！欢迎加入学习计划！',
        type: 'success',
        duration: 3000,
        showClose: true
      })

      // 延迟后重新获取绑定信息
      setTimeout(async () => {
        await getBindingInfo()
        // 初始化学生数据
        await studentStore.initStudentData()
        showSuccessAnimation.value = false
        // 清空表单
        form.bindCode = ''

        // 如果绑定信息获取成功，显示额外的成功提示
        if (binding.value) {
          ElMessage({
            message: `🎉 已成功绑定监督者：${binding.value.supervisor?.nickname || '监督者'}`,
            type: 'success',
            duration: 3000
          })
        }
      }, 1500)
    } else {
      throw new Error('绑定响应格式错误')
    }
  } catch (error) {
    console.error('绑定错误:', error)

    // 优化错误提示
    let errorMessage = '绑定失败，请重试'

    if (error.response?.status === 400) {
      errorMessage = '绑定码无效或已过期，请向监督者获取新的绑定码'
    } else if (error.response?.status === 404) {
      errorMessage = '绑定码不存在，请检查输入是否正确'
    } else if (error.response?.status === 409) {
      errorMessage = '您已经绑定了监督者，请先解除当前绑定'
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    }

    ElMessage({
      message: `❌ ${errorMessage}`,
      type: 'error',
      duration: 4000,
      showClose: true
    })
  } finally {
    isSubmitting.value = false
    loadingText.value = '确认绑定'
  }
}

// 确认解除绑定
const confirmUnbind = () => {
  ElMessageBox.confirm(
    '确定要解除与监督者的绑定关系吗？解除后将无法查看已发布的任务。',
    '解除绑定',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    unbind()
  }).catch(() => {})
}

// 解除绑定
const unbind = async () => {
  try {
    await bindingAPI.unbind({
      binding_id: binding.value.binding_id
    })

    toast.success('解除绑定成功')
    binding.value = null
  } catch (error) {
    console.error('解除绑定错误:', error)
    toast.error(error.response?.data?.message || '解除绑定失败，请重试')
  }
}

// 格式化日期
const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

// 计算绑定天数
const calculateBindingDays = () => {
  if (!binding.value?.created_at) return 0
  return dayjs().diff(dayjs(binding.value.created_at), 'day') + 1
}

onMounted(() => {
  getBindingInfo()
})
</script>

<style scoped>
.card-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.form-container {
  max-width: 400px;
  margin: 0 auto;
}

.text-center {
  text-align: center;
}

.w-full {
  width: 100%;
}

/* 成功动画样式 */
.success-animation {
  text-align: center;
  animation: fadeInUp 0.6s ease-out;
}

.success-icon {
  margin-bottom: 16px;
  animation: bounceIn 0.8s ease-out;
}

.success-text {
  font-size: 18px;
  font-weight: 600;
  color: #67C23A;
  animation: fadeIn 1s ease-out 0.3s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 监督者卡片样式 */
.supervisor-card {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.supervisor-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
}

.supervisor-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin-right: 20px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.supervisor-info {
  flex: 1;
}

.supervisor-name {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.supervisor-role {
  margin-bottom: 8px;
}

.supervisor-email,
.binding-time {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #64748b;
  font-size: 14px;
  margin-bottom: 4px;
}

.supervisor-stats {
  display: flex;
  gap: 24px;
  margin: 20px 0;
  padding: 16px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.supervisor-actions {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}
</style>