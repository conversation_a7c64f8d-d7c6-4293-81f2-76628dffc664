import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue')
  },
  {
    path: '/tasks-test',
    name: 'TasksTest',
    component: () => import('../views/TasksTest.vue')
  },
  {
    path: '/simple-test',
    name: 'SimpleTest',
    component: () => import('../views/SimpleTest.vue')
  },
  {
    path: '/realtime-test',
    name: 'RealtimeTest',
    component: () => import('../views/test/RealtimeTest.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/data-consistency-test',
    name: 'DataConsistencyTest',
    component: () => import('../views/test/DataConsistencyTest.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue')
  },
  // 监督者路由
  {
    path: '/supervisor',
    name: 'SupervisorDashboard',
    component: () => import('../views/supervisor/Dashboard.vue'),
    meta: { requiresAuth: true, role: 'supervisor' },
    children: [
      {
        path: 'binding',
        name: 'SupervisorBinding',
        component: () => import('../views/supervisor/Binding.vue'),
      },
      {
        path: 'tasks',
        name: 'SupervisorTasks',
        component: () => import('../views/supervisor/Tasks.vue'),
      },
      {
        path: 'create-task',
        name: 'CreateTask',
        component: () => import('../views/supervisor/CreateTask.vue'),
      },
      {
        path: 'task/:id',
        name: 'TaskReview',
        component: () => import('../views/supervisor/TaskReview.vue'),
      },
      {
        path: 'statistics',
        name: 'SupervisorStatistics',
        component: () => import('../views/supervisor/Statistics.vue'),
      },
      {
        path: 'rewards',
        name: 'SupervisorRewards',
        component: () => import('../views/supervisor/Rewards.vue'),
      },
      {
        path: 'student/:studentId',
        name: 'StudentTaskDetail',
        component: () => import('../views/supervisor/StudentTaskDetail.vue'),
      }
    ]
  },
  // 考研者路由 - 新版布局
  {
    path: '/student',
    component: () => import('../layouts/StudentLayout.vue'),
    meta: { requiresAuth: true, role: 'student' },
    children: [
      {
        path: '',
        redirect: '/student/university'
      },
      {
        path: 'university',
        name: 'StudentUniversity',
        component: () => import('../views/student/University.vue')
      },
      {
        path: 'binding',
        name: 'StudentBinding',
        component: () => import('../views/student/Binding.vue')
      },
      {
        path: 'tasks',
        name: 'StudentTasks',
        component: () => import('../views/student/Tasks.vue')
      },
      {
        path: 'my-tasks',
        name: 'StudentMyTasks',
        component: () => import('../views/student/MyTasks.vue')
      },
      {
        path: 'profile',
        name: 'StudentProfile',
        component: () => import('../views/student/Profile.vue')
      },
      {
        path: 'points-shop',
        name: 'PointsShop',
        component: () => import('../views/student/PointsShop.vue')
      },
      {
        path: 'task/:id/submit',
        name: 'TaskSubmit',
        component: () => import('../views/student/TaskSubmit.vue')
      }
    ]
  },
  // 重定向旧路径到新路径
  {
    path: '/student-university',
    redirect: '/student/university'
  },
  {
    path: '/student-tasks',
    redirect: '/student/tasks'
  },
  {
    path: '/student-my-tasks',
    redirect: '/student/my-tasks'
  },
  {
    path: '/student-profile',
    redirect: '/student/profile'
  },
  {
    path: '/student-task/:id/submit',
    redirect: to => `/student/task/${to.params.id}/submit`
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 测试页面直接放行
  if (to.path === '/tasks-test' || to.path === '/simple-test' || to.path === '/realtime-test' || to.path === '/data-consistency-test') {
    next()
    return
  }
  
  const authStore = useAuthStore()
  
  // 需要登录的页面
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } 
  // 需要特定角色的页面
  else if (to.meta.role && to.meta.role !== authStore.userRole) {
    next('/')
  }
  // 其他情况正常跳转
  else {
    next()
  }
})

export default router