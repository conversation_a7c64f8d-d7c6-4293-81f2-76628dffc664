const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const userController = require('../controllers/userController');
const taskController = require('../controllers/taskController');
const rewardController = require('../controllers/rewardController');
const { authenticate } = require('../middleware/auth');

// 身份验证路由
router.post('/auth/register', authController.register);
router.post('/auth/login', authController.login);
router.get('/auth/me', authenticate, authController.getCurrentUser);
router.post('/auth/change-password', authenticate, authController.changePassword);
router.post('/auth/reset-password', authController.resetPassword);

// 用户路由
router.get('/users', authenticate, userController.getAllStudents);
router.get('/users/:id', authenticate, userController.getUserById);
router.put('/users/:id', authenticate, userController.updateUser);
router.put('/users/:id/points', authenticate, userController.updateUserPoints);
router.get('/users/:id/points-history', authenticate, userController.getUserPointHistory);

// 任务路由
const taskRoutes = require('./taskRoutes');
router.use('/tasks', taskRoutes);

// 奖励路由
router.post('/rewards', authenticate, rewardController.createReward);
router.get('/rewards', authenticate, rewardController.getRewards);
router.get('/rewards/:id', authenticate, rewardController.getRewardById);
router.put('/rewards/:id', authenticate, rewardController.updateReward);
router.delete('/rewards/:id', authenticate, rewardController.deleteReward);
router.post('/rewards/:id/redeem', authenticate, rewardController.redeemReward);
router.get('/redemptions', authenticate, rewardController.getUserRedemptions);
router.get('/admin/redemptions', authenticate, rewardController.getAllRedemptions);
router.put('/redemptions/:id/status', authenticate, rewardController.updateRedemptionStatus);

// 绑定路由
const bindingRoutes = require('./bindingRoutes');
router.use('/binding', bindingRoutes);

module.exports = router;