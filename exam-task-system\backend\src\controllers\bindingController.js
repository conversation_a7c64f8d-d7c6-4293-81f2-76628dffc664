const Binding = require('../models/Binding');
const TaskCompletion = require('../models/TaskCompletion');
const Point = require('../models/Point');
const Task = require('../models/Task');
const jwt = require('jsonwebtoken');

// 生成绑定码（监督者）
const generateBindCode = async (req, res, next) => {
  try {
    const { supervisor_id } = req.body;
    
    // 验证监督者ID
    if (!supervisor_id) {
      return res.status(400).json({ message: '监督者ID不能为空' });
    }

    // 检查是否已有绑定关系
    const existingBinding = await Binding.getBindingBySupervisor(supervisor_id);
    if (existingBinding) {
      // 如果已有绑定码但未被使用，返回现有绑定码
      if (existingBinding.status === 'pending') {
        return res.json({
          message: '绑定码已存在',
          bind_code: existingBinding.bind_code,
          status: existingBinding.status,
          created_at: existingBinding.created_at
        });
      } else {
        return res.status(400).json({ message: '监督者已有活跃的绑定关系' });
      }
    }

    // 创建新的绑定关系
    const binding = await Binding.createBinding({ supervisor_id });
    
    res.status(201).json({
      message: '绑定码生成成功',
      bind_code: binding.bind_code,
      status: binding.status,
      created_at: binding.created_at
    });
  } catch (error) {
    console.error('生成绑定码失败:', error);
    if (error.message === '监督者已有绑定关系') {
      return res.status(400).json({ message: error.message });
    }
    next(error);
  }
};

// 确认绑定（学生输入绑定码）
const confirmBinding = async (req, res, next) => {
  try {
    const { student_id, bind_code } = req.body;
    
    // 验证参数
    if (!student_id || !bind_code) {
      return res.status(400).json({ message: '学生ID和绑定码不能为空' });
    }

    // 验证绑定码格式
    if (!/^[A-Z0-9]{6}$/.test(bind_code)) {
      return res.status(400).json({ message: '绑定码格式不正确' });
    }

    // 确认绑定
    const binding = await Binding.confirmBinding(student_id, bind_code);
    
    // 获取完整的绑定信息
    const bindingWithUsers = await Binding.getBindingWithUsers(binding.id);
    
    res.json({
      message: '绑定成功',
      data: bindingWithUsers
    });
  } catch (error) {
    console.error('确认绑定失败:', error);
    if (error.message.includes('绑定码') || error.message.includes('学生已有') || error.message.includes('已被使用')) {
      return res.status(400).json({ message: error.message });
    }
    next(error);
  }
};

// 获取绑定信息
const getBindingInfo = async (req, res, next) => {
  try {
    const { user_id, role } = req.query;
    
    if (!user_id || !role) {
      return res.status(400).json({ message: '用户ID和角色不能为空' });
    }

    let binding = null;
    
    if (role === 'supervisor') {
      binding = await Binding.getBindingBySupervisor(user_id);
    } else if (role === 'student') {
      binding = await Binding.getBindingByStudent(user_id);
    } else {
      return res.status(400).json({ message: '无效的用户角色' });
    }

    if (!binding) {
      return res.json({
        message: '未找到绑定信息',
        data: null
      });
    }

    // 获取完整的绑定信息
    const bindingWithUsers = await Binding.getBindingWithUsers(binding.id);

    // 如果是学生请求，添加任务统计信息
    if (req.user.role === 'student') {
      const taskStats = await getStudentTaskStats(req.user.id);
      bindingWithUsers.task_stats = taskStats;
    }

    res.json({
      message: '获取成功',
      data: bindingWithUsers
    });
  } catch (error) {
    console.error('获取绑定信息失败:', error);
    next(error);
  }
};

// 解除绑定
const unbind = async (req, res, next) => {
  try {
    const { binding_id } = req.body;
    
    if (!binding_id) {
      return res.status(400).json({ message: '绑定ID不能为空' });
    }

    // 验证绑定是否存在
    const binding = await Binding.getBindingById(binding_id);
    if (!binding) {
      return res.status(404).json({ message: '绑定关系不存在' });
    }

    if (binding.status !== 'active') {
      return res.status(400).json({ message: '绑定关系已失效' });
    }

    // 解除绑定
    await Binding.unbind(binding_id);
    
    res.json({
      message: '解除绑定成功'
    });
  } catch (error) {
    console.error('解除绑定失败:', error);
    next(error);
  }
};

// 获取监督者的学生列表
const getStudentList = async (req, res, next) => {
  try {
    const { supervisor_id } = req.query;

    if (!supervisor_id) {
      return res.status(400).json({ message: '监督者ID不能为空' });
    }

    const students = await Binding.getStudentsBySupervisor(supervisor_id);

    // 为每个学生添加任务统计信息
    const studentsWithStats = await Promise.all(
      students.map(async (student) => {
        const taskStats = await getStudentTaskStats(student.student.id);
        return {
          ...student,
          task_stats: taskStats
        };
      })
    );

    res.json({
      message: '获取成功',
      data: studentsWithStats
    });
  } catch (error) {
    console.error('获取学生列表失败:', error);
    next(error);
  }
};

// 获取当前用户的绑定状态（从token中获取用户信息）
const getMyBinding = async (req, res, next) => {
  try {
    // 从认证中间件获取用户信息
    const { id: userId, role } = req.user;

    let binding = null;
    
    if (role === 'supervisor') {
      binding = await Binding.getBindingBySupervisor(userId);
    } else if (role === 'student') {
      binding = await Binding.getBindingByStudent(userId);
    } else {
      return res.status(400).json({ message: '无效的用户角色' });
    }

    if (!binding) {
      return res.json({
        message: '未找到绑定信息',
        data: null
      });
    }

    // 获取完整的绑定信息
    const bindingWithUsers = await Binding.getBindingWithUsers(binding.id);

    // 如果是学生，获取任务统计信息
    if (role === 'student') {
      try {
        // 获取学生的任务统计
        const taskStats = await getStudentTaskStats(userId);
        bindingWithUsers.task_stats = taskStats;
      } catch (error) {
        console.error('获取任务统计失败:', error);
        // 不影响主要功能，设置默认值
        bindingWithUsers.task_stats = {
          accepted_tasks: 0,
          completed_tasks: 0,
          total_points: 0,
          completion_rate: 0
        };
      }
    }

    res.json({
      message: '获取成功',
      data: bindingWithUsers
    });
  } catch (error) {
    console.error('获取我的绑定信息失败:', error);
    next(error);
  }
};

// 获取学生任务统计信息
const getStudentTaskStats = async (studentId) => {
  try {
    // 获取学生的所有任务（包括已接受的任务）
    const studentTasks = await Task.getTasks({ studentId });

    // 获取学生的任务完成记录
    const completions = await TaskCompletion.getCompletionsByStudent(studentId);

    // 统计数据
    const acceptedTasks = studentTasks.length;
    // 修改统计逻辑：包含已提交待审核的任务
    const completedTasks = completions.filter(c =>
      c.status === 'approved' || c.status === 'pending_review'
    ).length;
    const completionRate = acceptedTasks > 0 ? Math.round((completedTasks / acceptedTasks) * 100) : 0;

    // 计算总学习时间（分钟）- 包含已提交的任务
    let totalStudyTime = 0;
    completions.forEach(completion => {
      // 包含已审核通过和待审核的任务时长
      if ((completion.status === 'approved' || completion.status === 'pending_review') &&
          completion.timeSpent && completion.timeSpent > 0) {
        totalStudyTime += completion.timeSpent;
      }
    });

    // 获取学生的积分记录
    let totalPoints = 0;
    try {
      const pointRecords = await Point.getPointsByUser(studentId);
      totalPoints = pointRecords.reduce((sum, record) => sum + record.points, 0);
    } catch (error) {
      console.error('获取积分记录失败:', error);
    }

    // 如果没有积分记录，根据已完成任务估算积分
    if (totalPoints === 0 && completedTasks > 0) {
      // 估算积分：假设每个任务平均20积分
      totalPoints = completedTasks * 20;
      console.log('估算积分:', totalPoints);
    }

    console.log('学生任务统计:', {
      studentId,
      acceptedTasks,
      completedTasks,
      totalPoints,
      completionRate,
      totalStudyTime
    });

    return {
      accepted_tasks: acceptedTasks,
      completed_tasks: completedTasks,
      total_points: totalPoints,
      completion_rate: completionRate,
      total_study_time: totalStudyTime
    };
  } catch (error) {
    console.error('获取学生任务统计失败:', error);
    return {
      accepted_tasks: 0,
      completed_tasks: 0,
      total_points: 0,
      completion_rate: 0
    };
  }
};

module.exports = {
  generateBindCode,
  confirmBinding,
  getBindingInfo,
  unbind,
  getStudentList,
  getMyBinding
};
