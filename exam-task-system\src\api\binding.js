import api from './index'

export const bindingAPI = {
  // 生成绑定码
  generateBindCode: (data) => api.post('/binding/generate', data),

  // 确认绑定
  confirmBinding: (data) => api.post('/binding/confirm', data),

  // 获取绑定信息
  getBindingInfo: (params) => api.get('/binding/info', { params }),

  // 获取我的绑定信息
  getMyBinding: () => api.get('/binding/my'),

  // 获取学生列表
  getStudentList: (params) => api.get('/binding/students', { params }),

  // 解除绑定
  unbind: (data) => api.post('/binding/unbind', data)
}