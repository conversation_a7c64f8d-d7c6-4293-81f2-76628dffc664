require('dotenv').config();

// {{ AURA-X: Modify - 移除SQLite配置，统一使用CloudBase. Approval: 寸止(ID:1734681600). }}
// 注意：此项目使用腾讯CloudBase作为数据库，不需要传统的SQL配置
// CloudBase配置在 cloudbase.js 中管理

module.exports = {
  // CloudBase环境配置
  cloudbase: {
    envId: process.env.CLOUDBASE_ENV_ID,
    secretId: process.env.CLOUDBASE_SECRET_ID,
    secretKey: process.env.CLOUDBASE_SECRET_KEY,
    region: 'ap-shanghai'
  },

  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  },

  // 服务器配置
  server: {
    port: process.env.PORT || 5000,
    nodeEnv: process.env.NODE_ENV || 'development'
  }
};
