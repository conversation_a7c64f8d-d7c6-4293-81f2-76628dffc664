<template>
  <div class="binding-page">
    <h1>师生绑定管理</h1>
    <p>管理与考研者的绑定关系</p>

    <!-- 生成绑定码卡片 -->
    <el-card class="binding-card">
      <template #header>
        <div class="card-header">
          <span>生成绑定码</span>
        </div>
      </template>
      <div class="binding-form">
        <p class="description">点击下方按钮生成绑定码，将绑定码分享给考研者完成绑定</p>
        <el-button
          type="primary"
          @click="generateBindCode"
          :loading="generating"
          size="large"
        >
          {{ generating ? '生成中...' : '生成绑定码' }}
        </el-button>

        <!-- 显示生成的绑定码 -->
        <div v-if="currentBindCode" class="bind-code-display">
          <h3>您的绑定码：</h3>
          <div class="bind-code">{{ currentBindCode }}</div>
          <p class="bind-code-tip">请将此绑定码分享给考研者</p>
        </div>
      </div>
    </el-card>

    <!-- 已绑定学生列表 -->
    <el-card class="students-card">
      <template #header>
        <div class="card-header">
          <span>已绑定的考研者</span>
          <div class="header-actions">
            <el-button type="text" @click="refreshStudents">刷新</el-button>
            <el-button type="text" style="color: #409eff;" @click="checkDatabaseStatus">
              检查状态
            </el-button>
            <el-button type="text" style="color: #f56c6c;" @click="confirmResetTestEnvironment">
              重置测试环境
            </el-button>
          </div>
        </div>
      </template>
      <div class="students-list">
        <div v-if="loading" class="loading">
          <el-skeleton :rows="3" animated />
        </div>
        <div v-else-if="students.length === 0" class="empty">
          <el-empty description="暂无绑定的考研者" />
        </div>
        <div v-else>
          <div v-for="student in students" :key="student.binding_id" class="student-item">
            <div class="student-info">
              <el-avatar :size="50">{{ student.student.username.charAt(0) }}</el-avatar>
              <div class="info">
                <h4>{{ student.student.username }}</h4>
                <p>{{ student.student.email }}</p>
                <p class="bind-time">绑定时间：{{ formatDate(student.created_at) }}</p>
              </div>
            </div>
            <div class="actions">
              <el-button type="primary" size="small" @click="viewStudentTasks(student)">
                查看任务
              </el-button>
              <el-button type="danger" size="small" @click="confirmUnbind(student.binding_id)">
                解除绑定
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { bindingAPI } from '@/api/binding';
import { useAuthStore } from '@/stores/auth';
import { useSyncStore } from '@/stores/sync';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';

const authStore = useAuthStore();
const syncStore = useSyncStore();
const router = useRouter();

// 响应式数据
const loading = ref(false);
const generating = ref(false);
const currentBindCode = ref('');
const students = ref([]);
const bindingInfo = ref(null);

// 计算属性 - 简化逻辑，避免复杂的响应式计算
const hasActiveBinding = computed(() => {
  return false; // 暂时简化，总是显示生成绑定码界面
});

// 生成绑定码
const generateBindCode = async () => {
  try {
    generating.value = true;
    const response = await bindingAPI.generateBindCode({
      supervisor_id: authStore.user.id
    });

    console.log('绑定码API响应:', response); // 调试日志

    if (response && response.bind_code) {
      currentBindCode.value = response.bind_code;
      ElMessage.success(response.message || '绑定码生成成功');
      // 刷新绑定信息
      await getBindingInfo();
    } else {
      console.error('响应格式错误:', response);
      ElMessage.error('绑定码生成失败：响应格式错误');
    }
  } catch (error) {
    console.error('生成绑定码失败:', error);
    ElMessage.error(error.response?.data?.message || '生成绑定码失败');
  } finally {
    generating.value = false;
  }
};

// 获取绑定信息
const getBindingInfo = async () => {
  try {
    loading.value = true;
    const response = await bindingAPI.getMyBinding();

    if (response.data && response.data.data) {
      bindingInfo.value = response.data.data;
      if (bindingInfo.value.bind_code) {
        currentBindCode.value = bindingInfo.value.bind_code;
      }
    }
  } catch (error) {
    console.error('获取绑定信息失败:', error);
    // 不显示错误消息，避免干扰用户体验
  } finally {
    loading.value = false;
  }
};

// 获取学生列表
const getStudentList = async () => {
  try {
    const response = await bindingAPI.getStudentList({
      supervisor_id: authStore.user.id
    });

    console.log('学生列表API响应:', response); // 调试日志

    if (response && response.data) {
      students.value = response.data;
    } else if (response && Array.isArray(response)) {
      students.value = response;
    } else {
      students.value = [];
    }

    console.log('学生列表数据:', students.value); // 调试日志
  } catch (error) {
    console.error('获取学生列表失败:', error);
    students.value = [];
    // 不显示错误消息，避免干扰用户体验
  }
};

// 刷新学生列表
const refreshStudents = async () => {
  await getStudentList();
  ElMessage.success('刷新成功');
};

// 确认解除绑定
const confirmUnbind = (bindingId) => {
  ElMessageBox.confirm(
    '确定要解除与该考研者的绑定关系吗？',
    '解除绑定',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    unbindStudent(bindingId);
  }).catch(() => {});
};

// 解除绑定
const unbindStudent = async (bindingId) => {
  try {
    await bindingAPI.unbind({ binding_id: bindingId });
    ElMessage.success('解除绑定成功');
    // 刷新列表
    await getStudentList();
    await getBindingInfo();
  } catch (error) {
    console.error('解除绑定失败:', error);
    ElMessage.error(error.response?.data?.message || '解除绑定失败');
  }
};

// 查看学生任务
const viewStudentTasks = (student) => {
  // 跳转到学生任务详情页面，传递学生信息
  router.push({
    name: 'StudentTaskDetail',
    params: { studentId: student.student.id },
    query: {
      studentName: student.student.username,
      studentEmail: student.student.email
    }
  });
};

// 格式化日期
const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm');
};

// {{ AURA-X: Add - 添加重置测试环境功能. Approval: 寸止(ID:1734683400). }}
// 确认重置测试环境
const confirmResetTestEnvironment = () => {
  ElMessageBox.confirm(
    '确定要重置测试环境吗？这将删除您的所有学生、任务、积分记录和绑定关系。此操作不可恢复！',
    '重置测试环境',
    {
      confirmButtonText: '确定重置',
      cancelButtonText: '取消',
      type: 'error',
      dangerouslyUseHTMLString: true
    }
  ).then(() => {
    resetTestEnvironment();
  }).catch(() => {});
};

// 重置测试环境
const resetTestEnvironment = async () => {
  try {
    loading.value = true;

    // 调用重置API
    const response = await fetch(`/api/tasks/supervisor/${authStore.user.id}/reset`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const result = await response.json();
      ElMessage.success(`重置成功！删除了 ${result.data.deletedStudents} 个学生、${result.data.deletedTasks} 个任务、${result.data.deletedBindings} 个绑定关系`);

      // 刷新页面数据
      await getBindingInfo();
      await getStudentList();

      // 清空当前绑定码
      currentBindCode.value = '';
    } else {
      const error = await response.json();
      ElMessage.error(error.message || '重置失败');
    }
  } catch (error) {
    console.error('重置测试环境失败:', error);
    ElMessage.error('重置失败，请重试');
  } finally {
    loading.value = false;
  }
};

// {{ AURA-X: Add - 添加数据库状态检查功能. Approval: 寸止(ID:1734683400). }}
// 检查数据库状态
const checkDatabaseStatus = async () => {
  try {
    loading.value = true;

    // 调用状态检查API
    const response = await fetch(`/api/tasks/supervisor/${authStore.user.id}/status`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const result = await response.json();
      const status = result.data;

      // 显示详细的状态信息
      ElMessage.info({
        message: `数据库状态：活跃绑定${status.activeBindings}个，总绑定${status.allBindings}个，学生用户${status.studentUsers}个，任务${status.supervisorTasks}个，完成记录${status.totalCompletions}个，积分记录${status.totalPoints}个`,
        duration: 8000,
        showClose: true
      });

      console.log('数据库状态详情:', status);
    } else {
      const error = await response.json();
      ElMessage.error(error.message || '检查状态失败');
    }
  } catch (error) {
    console.error('检查数据库状态失败:', error);
    ElMessage.error('检查状态失败，请重试');
  } finally {
    loading.value = false;
  }
};

// {{ AURA-X: Modify - 添加实时同步初始化. Approval: 寸止(ID:1734682800). }}
// 实时数据更新处理
const handleBindingsUpdate = (event) => {
  console.log('接收到绑定关系实时更新:', event.detail);
  getStudentList(); // 重新获取学生列表
};

// 页面加载时获取数据 - 简化初始化逻辑
onMounted(async () => {
  try {
    // 初始化同步服务
    if (!syncStore.isInitialized) {
      await syncStore.initSync();
    }

    // 启动用户相关订阅
    syncStore.startUserSubscriptions();

    // 监听实时数据更新事件
    window.addEventListener('bindings-updated', handleBindingsUpdate);

    // 加载初始数据
    await getBindingInfo();
    await getStudentList();
  } catch (error) {
    console.error('初始化失败:', error);
  }
});

// 组件卸载时清理
onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('bindings-updated', handleBindingsUpdate);
});
</script>

<style scoped>
.binding-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.binding-card, .students-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.binding-form {
  text-align: center;
  padding: 20px;
}

.description {
  color: #666;
  margin-bottom: 20px;
}

.bind-code-display {
  margin-top: 30px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.bind-code {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  letter-spacing: 4px;
  margin: 10px 0;
}

.bind-code-tip {
  color: #999;
  font-size: 14px;
}

.students-list {
  min-height: 200px;
}

.student-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.student-item:last-child {
  border-bottom: none;
}

.student-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.info h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.bind-time {
  color: #999 !important;
  font-size: 12px !important;
}

.loading, .empty {
  padding: 40px;
  text-align: center;
}
</style>