<!-- {{ AURA-X: Add - 创建实时同步测试页面. Approval: 寸止(ID:1734682800). }} -->
<template>
  <div class="realtime-test">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>实时同步测试</span>
          <el-tag :type="connectionStatus.type">{{ connectionStatus.text }}</el-tag>
        </div>
      </template>

      <!-- 连接状态 -->
      <div class="status-section">
        <h3>连接状态</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="连接状态">
            <el-tag :type="syncStore.isConnected ? 'success' : 'danger'">
              {{ syncStore.isConnected ? '已连接' : '未连接' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="初始化状态">
            <el-tag :type="syncStore.isInitialized ? 'success' : 'warning'">
              {{ syncStore.isInitialized ? '已初始化' : '未初始化' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="活跃订阅数">
            {{ syncStore.subscriptions.size }}
          </el-descriptions-item>
          <el-descriptions-item label="最后同步时间">
            {{ syncStore.lastSyncTime ? formatTime(syncStore.lastSyncTime) : '无' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 操作按钮 -->
      <div class="actions-section">
        <h3>操作</h3>
        <el-space wrap>
          <el-button 
            type="primary" 
            @click="initSync"
            :loading="loading"
            :disabled="syncStore.isInitialized"
          >
            初始化同步
          </el-button>
          <el-button 
            type="success" 
            @click="startSubscriptions"
            :disabled="!syncStore.isInitialized"
          >
            启动订阅
          </el-button>
          <el-button 
            type="warning" 
            @click="stopSubscriptions"
            :disabled="!syncStore.hasActiveSubscriptions"
          >
            停止订阅
          </el-button>
          <el-button 
            type="info" 
            @click="refreshData"
          >
            刷新数据
          </el-button>
          <el-button 
            type="danger" 
            @click="destroySync"
          >
            销毁连接
          </el-button>
        </el-space>
      </div>

      <!-- 缓存数据 -->
      <div class="cache-section">
        <h3>缓存数据</h3>
        <el-tabs v-model="activeTab">
          <el-tab-pane label="任务数据" name="tasks">
            <div class="data-display">
              <p>任务数量: {{ cachedTasks.length }}</p>
              <el-table :data="cachedTasks.slice(0, 5)" size="small" max-height="200">
                <el-table-column prop="_id" label="ID" width="120" />
                <el-table-column prop="title" label="标题" />
                <el-table-column prop="status" label="状态" width="80" />
                <el-table-column prop="updatedAt" label="更新时间" width="150">
                  <template #default="{ row }">
                    {{ formatTime(row.updatedAt) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="绑定关系" name="bindings">
            <div class="data-display">
              <p>绑定数量: {{ cachedBindings.length }}</p>
              <el-table :data="cachedBindings.slice(0, 5)" size="small" max-height="200">
                <el-table-column prop="_id" label="ID" width="120" />
                <el-table-column prop="supervisor_id" label="监督者ID" width="120" />
                <el-table-column prop="student_id" label="学生ID" width="120" />
                <el-table-column prop="status" label="状态" width="80" />
              </el-table>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="积分记录" name="points">
            <div class="data-display">
              <p>积分记录数量: {{ cachedPoints.length }}</p>
              <el-table :data="cachedPoints.slice(0, 5)" size="small" max-height="200">
                <el-table-column prop="_id" label="ID" width="120" />
                <el-table-column prop="userId" label="用户ID" width="120" />
                <el-table-column prop="amount" label="积分" width="80" />
                <el-table-column prop="type" label="类型" width="80" />
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 事件日志 -->
      <div class="events-section">
        <h3>事件日志</h3>
        <div class="events-log">
          <div 
            v-for="(event, index) in events" 
            :key="index" 
            class="event-item"
            :class="event.type"
          >
            <span class="event-time">{{ formatTime(event.time) }}</span>
            <span class="event-type">{{ event.type }}</span>
            <span class="event-message">{{ event.message }}</span>
          </div>
        </div>
        <el-button size="small" @click="clearEvents">清空日志</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useSyncStore } from '@/stores/sync'
import { useAuthStore } from '@/stores/auth'

// 状态管理
const syncStore = useSyncStore()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const activeTab = ref('tasks')
const events = ref([])

// 计算属性
const connectionStatus = computed(() => {
  if (syncStore.isConnected) {
    return { type: 'success', text: '已连接' }
  } else if (syncStore.syncStatus === 'connecting') {
    return { type: 'warning', text: '连接中' }
  } else if (syncStore.syncStatus === 'error') {
    return { type: 'danger', text: '连接错误' }
  } else {
    return { type: 'info', text: '未连接' }
  }
})

const cachedTasks = computed(() => syncStore.getCachedTasks())
const cachedBindings = computed(() => syncStore.getCachedBindings())
const cachedPoints = computed(() => syncStore.getCachedPoints())

// 方法
const addEvent = (type, message) => {
  events.value.unshift({
    type,
    message,
    time: new Date()
  })
  // 限制日志数量
  if (events.value.length > 50) {
    events.value = events.value.slice(0, 50)
  }
}

const initSync = async () => {
  loading.value = true
  try {
    await syncStore.initSync()
    addEvent('success', '同步服务初始化成功')
    ElMessage.success('同步服务初始化成功')
  } catch (error) {
    addEvent('error', `初始化失败: ${error.message}`)
    ElMessage.error('初始化失败')
  } finally {
    loading.value = false
  }
}

const startSubscriptions = () => {
  try {
    syncStore.startUserSubscriptions()
    addEvent('info', '已启动用户订阅')
    ElMessage.success('订阅已启动')
  } catch (error) {
    addEvent('error', `启动订阅失败: ${error.message}`)
    ElMessage.error('启动订阅失败')
  }
}

const stopSubscriptions = () => {
  try {
    syncStore.stopAllSubscriptions()
    addEvent('warning', '已停止所有订阅')
    ElMessage.success('订阅已停止')
  } catch (error) {
    addEvent('error', `停止订阅失败: ${error.message}`)
    ElMessage.error('停止订阅失败')
  }
}

const refreshData = () => {
  addEvent('info', '手动刷新数据')
  ElMessage.info('数据已刷新')
}

const destroySync = () => {
  try {
    syncStore.destroySync()
    addEvent('warning', '同步服务已销毁')
    ElMessage.success('连接已销毁')
  } catch (error) {
    addEvent('error', `销毁失败: ${error.message}`)
    ElMessage.error('销毁失败')
  }
}

const clearEvents = () => {
  events.value = []
}

const formatTime = (date) => {
  if (!date) return ''
  const d = new Date(date)
  return `${d.getHours().toString().padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}:${d.getSeconds().toString().padStart(2, '0')}`
}

// 事件监听
const handleTasksUpdate = (event) => {
  addEvent('success', `任务数据更新: ${event.detail.changes?.length || 0} 个变更`)
}

const handleBindingsUpdate = (event) => {
  addEvent('success', `绑定关系更新: ${event.detail.changes?.length || 0} 个变更`)
}

const handlePointsUpdate = (event) => {
  addEvent('success', `积分数据更新: ${event.detail.changes?.length || 0} 个变更`)
}

// 生命周期
onMounted(() => {
  // 监听实时更新事件
  window.addEventListener('tasks-updated', handleTasksUpdate)
  window.addEventListener('bindings-updated', handleBindingsUpdate)
  window.addEventListener('points-updated', handlePointsUpdate)
  
  addEvent('info', '测试页面已加载')
})

onUnmounted(() => {
  // 清理事件监听
  window.removeEventListener('tasks-updated', handleTasksUpdate)
  window.removeEventListener('bindings-updated', handleBindingsUpdate)
  window.removeEventListener('points-updated', handlePointsUpdate)
})
</script>

<style scoped>
.realtime-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-section,
.actions-section,
.cache-section,
.events-section {
  margin-bottom: 30px;
}

.data-display {
  margin-top: 10px;
}

.events-log {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
  background-color: #f8f9fa;
}

.event-item {
  display: flex;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
  font-size: 12px;
}

.event-item:last-child {
  border-bottom: none;
}

.event-time {
  color: #909399;
  min-width: 60px;
}

.event-type {
  min-width: 60px;
  font-weight: bold;
}

.event-item.success .event-type {
  color: #67c23a;
}

.event-item.error .event-type {
  color: #f56c6c;
}

.event-item.warning .event-type {
  color: #e6a23c;
}

.event-item.info .event-type {
  color: #409eff;
}

.event-message {
  flex: 1;
}
</style>
